"""
Simple Financial Document Processing Integration
Date: 2025-06-30 10:31:16 UTC
User: Gangatharan G
Purpose: Simple linear integration of all existing components
"""

import os
import json
import asyncio
from pathlib import Path
from datetime import datetime
import argparse
import logging

# Import all existing components
from web_search import run_downloader
from classify import EnhancedPDFFinancialClassifier
from single_file_debt_equity_processor import SingleFileDebtEquityProcessor
from financial_data_pipeline_new import extract_consolidated_financials
from prompt_scehma_new import assets_prompt, cashflows_prompt, revenue_expenses_prompt, credit_rating_prompt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_integration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleFinancialProcessor:
    """Simple linear processor connecting all components"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        logger.info("Simple Financial Processor initialized")
        
        # Validate environment
        self.validate_environment()
    
    def validate_environment(self):
        """Check required environment variables"""
        required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("✅ Environment variables validated")
    
    def find_classified_images(self, output_dir: Path, classification_type: str):
        """Find first image for a classification type"""
        classified_dir = output_dir / "classified_images"
        
        # Map to directory names (from classify.py)
        dir_mapping = {
            "balance_sheet": "statement_of_balance_sheet",
            "profit_loss": "statement_of_profit_and_loss", 
            "cash_flows": "statement_of_cash_flows",
            "credit_rating": "credit_rating"
        }
        
        target_dir = classified_dir / dir_mapping.get(classification_type, classification_type)
        
        if target_dir.exists():
            images = list(target_dir.glob("*.png"))
            if images:
                return str(images[0])  # Return first image
        
        return None
    
    async def process_plant_simple(self, plant_name: str, use_local_pdfs: list = None):
        """
        Simple linear processing pipeline
        
        Args:
            plant_name: "Renew Akshay Urja Limited"
            use_local_pdfs: ["annual_2020.pdf", "annual_2021.pdf", ...] or None
        """
        
        print(f"🚀 Simple Financial Processing Pipeline")
        print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC")
        print(f"👤 User: Gangatharan G")
        print(f"🏭 Plant: {plant_name}")
        print("="*80)
        
        results = {
            "plant_name": plant_name,
            "processing_timestamp": datetime.utcnow().isoformat(),
            "user": "Gangatharan G",
            "debt_equity_analysis": None,
            "financial_data": None,
            "processing_steps": []
        }
        
        try:
            # Step 1: Get PDFs
            print("\n📄 STEP 1: Getting PDF files...")
            results["processing_steps"].append("Step 1: PDF Acquisition")
            
            if use_local_pdfs:
                print(f"✅ Using {len(use_local_pdfs)} local PDF files")
                pdf_files = use_local_pdfs
                
                # Validate local files exist
                for pdf_file in pdf_files:
                    if not Path(pdf_file).exists():
                        raise FileNotFoundError(f"Local PDF not found: {pdf_file}")
            else:
                print("🔍 Downloading PDFs using web search...")
                
                # Use friend's web search (assumes it downloads to downloads/{plant_name}/)
                run_downloader(plant_name, 2020, 2024)
                
                # Find downloaded PDFs
                safe_plant_name = plant_name.replace(" ", "_").replace('"', '')
                downloads_dir = Path("downloads") / safe_plant_name
                
                if downloads_dir.exists():
                    pdf_files = list(downloads_dir.glob("*.pdf"))
                    pdf_files = [str(f) for f in pdf_files]
                    print(f"✅ Found {len(pdf_files)} downloaded PDFs")
                else:
                    raise FileNotFoundError(f"No PDFs found in downloads directory: {downloads_dir}")
            
            if not pdf_files:
                raise ValueError("No PDF files available for processing")
            
            # Step 2: Classification
            print("\n📊 STEP 2: PDF Classification...")
            results["processing_steps"].append("Step 2: PDF Classification")
            
            # Create output directory
            safe_plant_name = plant_name.replace(" ", "_").replace('"', '').lower()
            output_dir = Path(f"{safe_plant_name}_output_{self.timestamp}")
            output_dir.mkdir(exist_ok=True)
            
            # Process first PDF with classification
            first_pdf = pdf_files[0]
            print(f"🔍 Classifying: {Path(first_pdf).name}")
            
            classifier = EnhancedPDFFinancialClassifier(batch_size=5)
            classification_results = classifier.classify_pdf_batch_by_batch(
                pdf_path=first_pdf,
                output_dir=str(output_dir)
            )
            
            # Find manifest file
            manifest_files = list(output_dir.glob("enhanced_vllm_manifest.json"))
            if not manifest_files:
                raise FileNotFoundError("Classification manifest not found")
            
            manifest_path = str(manifest_files[0])
            print(f"✅ Classification completed: {len(classification_results)} results")
            
            # Step 3: Debt-Equity Processing
            print("\n💰 STEP 3: Debt-Equity Analysis...")
            results["processing_steps"].append("Step 3: Debt-Equity Analysis")
            
            debt_processor = SingleFileDebtEquityProcessor()
            debt_equity_data = await debt_processor.process_single_file_adaptive(manifest_path)
            
            results["debt_equity_analysis"] = debt_equity_data
            print("✅ Debt-equity analysis completed")
            
            # Step 4: Financial Data Extraction
            print("\n📈 STEP 4: Financial Data Extraction...")
            results["processing_steps"].append("Step 4: Financial Data Extraction")
            
            # Find required classified images
            balance_sheet_path = self.find_classified_images(output_dir, "balance_sheet")
            profit_loss_path = self.find_classified_images(output_dir, "profit_loss")
            cash_flows_path = self.find_classified_images(output_dir, "cash_flows")
            credit_rating_path = self.find_classified_images(output_dir, "credit_rating")
            
            print(f"📊 Found images:")
            print(f"  • Balance Sheet: {'✅' if balance_sheet_path else '❌'}")
            print(f"  • Profit & Loss: {'✅' if profit_loss_path else '❌'}")
            print(f"  • Cash Flows: {'✅' if cash_flows_path else '❌'}")
            print(f"  • Credit Rating: {'✅' if credit_rating_path else '❌'}")
            
            # Extract financial data
            financial_output_path = output_dir / f"financial_data_{self.timestamp}.json"
            
            if balance_sheet_path or profit_loss_path or cash_flows_path or credit_rating_path:
                # Use placeholder images if some are missing
                balance_sheet_path = balance_sheet_path or profit_loss_path or cash_flows_path
                profit_loss_path = profit_loss_path or balance_sheet_path or cash_flows_path
                cash_flows_path = cash_flows_path or balance_sheet_path or profit_loss_path
                credit_rating_path = credit_rating_path or balance_sheet_path
                
                print("🔍 Running financial data extraction...")
                extract_consolidated_financials(
                    balance_sheet_path=balance_sheet_path,
                    profit_and_loss_path=profit_loss_path,
                    cash_flows_path=cash_flows_path,
                    credit_rating_path=credit_rating_path,
                    assets_prompt=assets_prompt,
                    cashflows_prompt=cashflows_prompt,
                    revenue_expenses_prompt=revenue_expenses_prompt,
                    credit_rating_prompt=credit_rating_prompt,
                    output_file_path=str(financial_output_path)
                )
                
                # Load financial data
                with open(financial_output_path, 'r') as f:
                    financial_data = json.load(f)
                
                results["financial_data"] = financial_data
                print("✅ Financial data extraction completed")
            else:
                print("⚠️ No classified financial images found - skipping financial extraction")
                results["financial_data"] = {"error": "No classified financial images available"}
            
            # Step 5: Final Output
            print("\n🔄 STEP 5: Creating final combined output...")
            results["processing_steps"].append("Step 5: Final Output Creation")
            
            # Save final combined results
            final_output_path = output_dir / f"{safe_plant_name}_complete_analysis_{self.timestamp}.json"
            
            with open(final_output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n🎉 PROCESSING COMPLETED SUCCESSFULLY!")
            print(f"📁 Output directory: {output_dir}")
            print(f"📄 Final results: {final_output_path}")
            print(f"📊 Debt-equity analysis: {'✅ Success' if results['debt_equity_analysis'] else '❌ Failed'}")
            print(f"📈 Financial data: {'✅ Success' if results['financial_data'] and 'error' not in results['financial_data'] else '❌ Failed'}")
            
            return str(final_output_path), results
            
        except Exception as e:
            error_msg = f"Processing failed: {str(e)}"
            print(f"\n❌ ERROR: {error_msg}")
            logger.error(error_msg)
            
            results["error"] = error_msg
            results["processing_steps"].append(f"Error: {error_msg}")
            
            return None, results

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(description="Simple Financial Document Processing Integration")
    parser.add_argument("plant_name", help="Plant name (e.g., 'Renew Akshay Urja Limited')")
    parser.add_argument("--local-pdfs", nargs="+", help="Local PDF files to process")
    parser.add_argument("--output-dir", help="Custom output directory")
    
    args = parser.parse_args()
    
    print(f"🚀 Simple Financial Processing Pipeline")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC") 
    print(f"👤 User: Gangatharan G")
    print(f"🏭 Plant: {args.plant_name}")
    
    # Check environment
    required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("\nRequired setup:")
        for var in missing_vars:
            print(f"export {var}='your_api_key_here'")
        return
    
    try:
        # Initialize processor
        processor = SimpleFinancialProcessor()
        
        # Run processing
        output_path, results = asyncio.run(
            processor.process_plant_simple(
                plant_name=args.plant_name,
                use_local_pdfs=args.local_pdfs
            )
        )
        
        if output_path:
            print(f"\n✅ Success! Results saved to: {output_path}")
            
            # Show quick summary
            debt_analysis = results.get('debt_equity_analysis', {})
            if debt_analysis and debt_analysis.get('debt_equity_analysis'):
                analysis = debt_analysis['debt_equity_analysis'][0]
                equity_pct = analysis.get('equity', {}).get('percentage')
                if equity_pct is not None:
                    print(f"📊 Quick Summary:")
                    print(f"  • Equity: {equity_pct*100:.1f}%")
                    print(f"  • Long-term Debt: {analysis.get('long_term', {}).get('percentage', 0)*100:.1f}%")
                    print(f"  • Short-term Debt: {analysis.get('short_term', {}).get('percentage', 0)*100:.1f}%")
        else:
            print(f"\n❌ Processing failed!")
            if 'error' in results:
                print(f"Error: {results['error']}")
        
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        logger.error(f"Fatal error: {e}")

if __name__ == "__main__":
    main()