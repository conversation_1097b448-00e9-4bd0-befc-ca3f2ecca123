accelerate==1.9.0
agent @ file:///Users/<USER>/Documents/mlprojects/agentic_layer
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
asyncio-throttle==1.0.2
attrs==25.3.0
backoff==2.2.1
beautifulsoup4==4.13.4
blockbuster==1.5.25
boto3==1.38.46
botocore==1.38.46
cachetools==5.5.2
certifi==2025.6.15
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
coloredlogs==15.0.1
contourpy==1.3.2
cryptography==44.0.3
cycler==0.12.1
dataclasses-json==0.6.7
Deprecated==1.2.18
distro==1.9.0
effdet==0.4.1
emoji==2.14.1
fastapi==0.115.14
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
fonttools==4.59.0
forbiddenfruit==0.1.4
frozenlist==1.7.0
fsspec==2025.7.0
google-ai-generativelanguage==0.6.15
google-api-core==2.25.1
google-api-python-client==2.174.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-cloud-vision==3.10.2
google-genai==1.26.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
greenlet==3.2.3
grpcio==1.73.1
grpcio-status==1.71.2
gunicorn==23.0.0
h11==0.16.0
hf-xet==1.1.5
html5lib==1.1
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.33.4
humanfriendly==10.0
idna==3.10
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema_rs==0.29.1
kiwisolver==1.4.8
langchain==0.3.26
langchain-community==0.3.26
langchain-core==0.3.70
langchain-google-genai==2.0.10
langchain-text-splitters==0.3.8
langdetect==1.0.9
langgraph==0.5.4
langgraph-api==0.2.98
langgraph-checkpoint==2.1.1
langgraph-cli==0.3.5
langgraph-prebuilt==0.5.2
langgraph-runtime-inmem==0.6.0
langgraph-sdk==0.1.74
langsmith==0.4.4
lxml==6.0.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
mdurl==0.1.2
mpmath==1.3.0
multidict==6.6.2
mypy_extensions==1.1.0
nest-asyncio==1.6.0
networkx==3.5
nltk==3.9.1
numpy==2.2.6
olefile==0.47
omegaconf==2.3.0
onnx==1.18.0
onnxruntime==1.22.1
openai==1.93.0
opencv-python==4.12.0.88
orjson==3.10.18
ormsgpack==1.10.0
outcome==1.3.0.post0
packaging==24.2
pandas==2.3.0
pdf2image==1.17.0
pdfminer.six==20250506
pdfplumber==0.11.7
pi_heif==1.0.0
pikepdf==9.10.2
pillow==11.2.1
playwright==1.53.0
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycocotools==2.0.10
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
pyee==13.0.0
Pygments==2.19.2
PyJWT==2.10.1
pyparsing==3.2.3
pypdf==5.8.0
pypdfium2==4.30.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-iso639==2025.2.18
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
pytz==2025.2
PyYAML==6.0.2
RapidFuzz==3.13.0
regex==2024.11.6
requests==2.32.4
requests-toolbelt==1.0.0
rich==14.0.0
rsa==4.9.1
s3transfer==0.13.0
safetensors==0.5.3
scipy==1.16.0
selenium==4.34.2
setuptools==78.1.1
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.7
SQLAlchemy==2.0.41
sse-starlette==2.1.3
starlette==0.46.2
structlog==25.4.0
sympy==1.14.0
tenacity==8.5.0
timm==1.0.17
tokenizers==0.21.2
torch==2.7.1
torchvision==0.22.1
tqdm==4.67.1
transformers==4.53.2
trio==0.30.0
trio-websocket==0.12.2
truststore==0.10.1
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
unstructured==0.18.9
unstructured-client==0.39.1
unstructured-inference==1.0.5
unstructured.pytesseract==0.3.15
uritemplate==4.2.0
urllib3==2.5.0
uvicorn==0.35.0
watchfiles==1.1.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
wrapt==1.17.2
wsproto==1.2.0
xxhash==3.5.0
yarl==1.20.1
zstandard==0.23.0
