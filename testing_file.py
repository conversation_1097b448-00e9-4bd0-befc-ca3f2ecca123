"""
Multi-Year Financial Document Processing Integration
Date: 2025-06-30 10:33:40 UTC
User: Gangatharan G
Purpose: Process multiple years and merge results
"""

import os
import json
import asyncio
from pathlib import Path
from datetime import datetime
import argparse
import logging
import re
from final_schema_transformer import FinalSchemaTransformer

# Import all existing components
from web_search import run_downloader
from classify import EnhancedPDFFinancialClassifier
from single_file_debt_equity_processor import SingleFileDebtEquityProcessor
from financial_data_pipeline_new import extract_consolidated_financials
from prompt_scehma_new import assets_prompt, cashflows_prompt, revenue_expenses_prompt, credit_rating_prompt
from process_output_file import merge_powerplant_jsons,create_final_output_with_static,get_unique_output_path
# from prompt_scehma_new import get_dynamic_prompts
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('multi_year_integration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MultiYearFinancialProcessor:
    """Multi-year processor for any plant worldwide"""
    
    def __init__(self):
        """
        Initialize the MultiYearFinancialProcessor.
        
        This method is called when the class is instantiated. It sets the timestamp
        to the current date and time, and then validates the environment variables.
        """
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        logger.info("Multi-Year Financial Processor initialized")
        
        # Validate environment
        self.validate_environment()
    
    def validate_environment(self):
        """Check required environment variables"""
        required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        logger.info("✅ Environment variables validated")
    
    def extract_year_from_filename(self, pdf_path: str) -> int:
        """Extract year from PDF filename"""
        filename = Path(pdf_path).name.lower()
        
        # Look for 4-digit year pattern
        year_match = re.search(r'20\d{2}', filename)
        if year_match:
            return int(year_match.group())
        
        # Default to current year if not found
        return datetime.now().year
    
    def find_classified_images(self, output_dir: Path, classification_type: str):
        """Find first image for a classification type"""
        classified_dir = output_dir / "classified_images"
        
        # Map to directory names (from classify.py)
        dir_mapping = {
            "balance_sheet": "statement_of_balance_sheet",
            "profit_loss": "statement_of_profit_and_loss", 
            "cash_flows": "statement_of_cash_flows",
            "credit_rating": "credit_rating"
        }
        
        target_dir = classified_dir / dir_mapping.get(classification_type, classification_type)
        
        if target_dir.exists():
            images = list(target_dir.glob("*.png"))
            if images:
                return str(images[0])  # Return first image
        
        return None
    
    async def process_single_year(self, pdf_path: str, year: int, plant_name: str, base_output_dir: Path):
        """Process a single year's PDF"""
        print(f"\n📅 Processing Year {year}: {Path(pdf_path).name}")
        
        year_result = {
            "year": year,
            "pdf_file": str(pdf_path),
            "debt_equity_analysis": None,
            "financial_data": None,
            "processing_status": "started"
        }
        
        try:
            # Create year-specific output directory
            year_output_dir = base_output_dir / f"year_{year}"
            year_output_dir.mkdir(exist_ok=True)
            
            # Step 1: Classification for this year
            print(f"  📊 Classifying {year} documents...")
            classifier = EnhancedPDFFinancialClassifier(batch_size=5)
            classification_results = classifier.classify_pdf_batch_by_batch(
                pdf_path=pdf_path,
                output_dir=str(year_output_dir)
            )
            
            # Find manifest file
            manifest_files = list(year_output_dir.glob("enhanced_vllm_manifest.json"))
            if not manifest_files:
                raise FileNotFoundError(f"Classification manifest not found for year {year}")
            
            manifest_path = str(manifest_files[0])
            print(f"  ✅ {year}: Classification completed")
            
            # Step 2: Debt-Equity Processing for this year
            print(f"  💰 Extracting debt-equity data for {year}...")
            debt_processor = SingleFileDebtEquityProcessor()
            debt_equity_data = await debt_processor.process_single_file_adaptive(manifest_path)
            
            year_result["debt_equity_analysis"] = debt_equity_data
            print(f"  ✅ {year}: Debt-equity analysis completed")
            
            # Step 3: Financial Data Extraction for this year
            print(f"  📈 Extracting financial data for {year}...")
            
            # Find required classified images
            balance_sheet_path = self.find_classified_images(year_output_dir, "balance_sheet")
            profit_loss_path = self.find_classified_images(year_output_dir, "profit_loss")
            cash_flows_path = self.find_classified_images(year_output_dir, "cash_flows")
            credit_rating_path = self.find_classified_images(year_output_dir, "credit_rating")
            
            # Extract financial data if images available
            financial_output_path = year_output_dir / f"financial_data_{year}.json"
            
            if balance_sheet_path or profit_loss_path or cash_flows_path or credit_rating_path:
                # Use placeholder images if some are missing
                balance_sheet_path = balance_sheet_path or profit_loss_path or cash_flows_path
                profit_loss_path = profit_loss_path or balance_sheet_path or cash_flows_path
                cash_flows_path = cash_flows_path or balance_sheet_path or profit_loss_path
                credit_rating_path = credit_rating_path or balance_sheet_path
                
                # NEW CODE:
                

                # Get year-specific prompts
                # year_prompts = get_dynamic_prompts(year)

                output_folder = os.path.join("outputs", plant_name)
                unique_output_file_path = get_unique_output_path(output_folder)

                extract_consolidated_financials(
                    balance_sheet_path=balance_sheet_path,
                    profit_and_loss_path=profit_loss_path,
                    cash_flows_path=cash_flows_path,
                    credit_rating_path=credit_rating_path,
                    assets_prompt=assets_prompt,
                    cashflows_prompt=cashflows_prompt,
                    revenue_expenses_prompt=revenue_expenses_prompt,
                    credit_rating_prompt=credit_rating_prompt,
                    output_file_path=unique_output_file_path
                )
                
                # Load financial data
                with open(financial_output_path, 'r') as f:
                    financial_data = json.load(f)
                
                year_result["financial_data"] = financial_data

                print(f"  ✅ {year}: Financial data extraction completed")
            else:
                print(f"  ⚠️ {year}: No classified financial images found")
                year_result["financial_data"] = {"error": "No classified financial images available"}
            
            year_result["processing_status"] = "completed"
            print(f"✅ Year {year} processing completed successfully!")
            
            return year_result
            
        except Exception as e:
            error_msg = f"Year {year} processing failed: {str(e)}"
            print(f"  ❌ {error_msg}")
            logger.error(error_msg)
            
            year_result["processing_status"] = "failed"
            year_result["error"] = str(e)
            
            return year_result
    
    def merge_multi_year_results(self, year_results: list) -> dict:
        """Merge results from multiple years"""
        print("\n🔄 Merging multi-year results...")
        
        merged_debt_equity = []
        merged_financial = {
            "assets": [],
            "cashflows": [],
            "revenue_expenses": [],
            "credit_rating": []
        }
        
        successful_years = []
        
        for year_result in year_results:
            if year_result["processing_status"] == "completed":
                successful_years.append(year_result["year"])
                
                # Merge debt-equity analysis
                debt_data = year_result.get("debt_equity_analysis", {})
                if debt_data and debt_data.get("debt_equity_analysis"):
                    debt_analysis = debt_data["debt_equity_analysis"][0]
                    debt_analysis["year"] = year_result["year"]  # Ensure year is set
                    merged_debt_equity.append(debt_analysis)
                
                # Merge financial data
                financial_data = year_result.get("financial_data", {})
                if financial_data and "error" not in financial_data:
                    # Add year to each financial component
                    for component in ["assets", "cashflows", "revenue_expenses", "credit_rating"]:
                        if component in financial_data:
                            component_data = financial_data[component]
                            if isinstance(component_data, list):
                                for item in component_data:
                                    if isinstance(item, dict):
                                        item["year"] = year_result["year"]
                                merged_financial[component].extend(component_data)
                            elif isinstance(component_data, dict):
                                component_data["year"] = year_result["year"]
                                merged_financial[component].append(component_data)
        
        print(f"✅ Merged data from {len(successful_years)} successful years: {successful_years}")
        
        return {
            "merged_debt_equity_analysis": merged_debt_equity,
            "merged_financial_data": merged_financial,
            "successful_years": successful_years,
            "total_years_processed": len(year_results),
            "successful_years_count": len(successful_years)
        }
    
    async def process_plant_multi_year(self, plant_name: str, use_local_pdfs: list = None, target_years: list = None):
        """
        Multi-year processing pipeline for any plant
        
        Args:
            plant_name: "Renew Akshay Urja Limited", "Adani Mundra", etc.
            use_local_pdfs: ["annual_2020.pdf", "annual_2021.pdf", ...] or None
            target_years: [2020, 2021, 2022, 2023, 2024] or None for all
        """
        
        print(f"🚀 Multi-Year Financial Processing Pipeline")
        print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC")
        print(f"👤 User: Gangatharan G")
        print(f"🏭 Plant: {plant_name}")
        print(f"📆 Target Years: {target_years or 'All available years'}")
        print("="*80)
        
        results = {
            "plant_name": plant_name,
            "processing_timestamp": datetime.utcnow().isoformat(),
            "user": "Gangatharan G",
            "target_years": target_years,
            "year_results": [],
            "merged_results": None,
            "processing_summary": {}
        }
        
        try:
            # Step 1: Get PDFs
            print("\n📄 STEP 1: Getting PDF files...")
            
            if use_local_pdfs:
                print(f"✅ Using {len(use_local_pdfs)} local PDF files")
                pdf_files = use_local_pdfs
                
                # Validate local files exist
                for pdf_file in pdf_files:
                    if not Path(pdf_file).exists():
                        raise FileNotFoundError(f"Local PDF not found: {pdf_file}")
            else:
                print("🔍 Downloading PDFs using web search...")
                
                # Use friend's web search (downloads 2020-2024 by default)
                run_downloader(plant_name, 2020, 2024)
                
                # Find downloaded PDFs
                safe_plant_name = plant_name.replace(" ", "_").replace('"', '')
                downloads_dir = Path("downloads") / safe_plant_name
                
                if downloads_dir.exists():
                    pdf_files = list(downloads_dir.glob("*.pdf"))
                    pdf_files = [str(f) for f in pdf_files]
                    print(f"✅ Found {len(pdf_files)} downloaded PDFs")
                else:
                    raise FileNotFoundError(f"No PDFs found in downloads directory: {downloads_dir}")
            
            if not pdf_files:
                raise ValueError("No PDF files available for processing")
            
            # Filter by target years if specified
            if target_years:
                filtered_pdfs = []
                for pdf_file in pdf_files:
                    pdf_year = self.extract_year_from_filename(pdf_file)
                    if pdf_year in target_years:
                        filtered_pdfs.append(pdf_file)
                
                pdf_files = filtered_pdfs
                print(f"📅 Filtered to {len(pdf_files)} PDFs matching target years")
            
            # Create main output directory
            safe_plant_name = plant_name.replace(" ", "_").replace('"', '').lower()
            main_output_dir = Path(f"{safe_plant_name}_multi_year_{self.timestamp}")
            main_output_dir.mkdir(exist_ok=True)
            
            # Step 2: Process each year
            print(f"\n📊 STEP 2: Processing {len(pdf_files)} years...")
            
            year_results = []
            for pdf_file in pdf_files:
                year = self.extract_year_from_filename(pdf_file)
                year_result = await self.process_single_year(pdf_file, year, plant_name, main_output_dir)
                year_results.append(year_result)
                results["year_results"].append(year_result)
            merge_powerplant_jsons(plant_name)
            create_final_output_with_static(plant_name)
            
            # Step 3: Merge multi-year results
            print("\n🔄 STEP 3: Merging multi-year results...")
            merged_results = self.merge_multi_year_results(year_results)
            results["merged_results"] = merged_results
            
            # Step 4: Create final comprehensive output
            print("\n📋 STEP 4: Creating comprehensive output...")
            
            final_output = {
                "plant_name": plant_name,
                "processing_info": {
                    "timestamp": datetime.utcnow().isoformat(),
                    "user": "Gangatharan G",
                    "total_years_processed": len(pdf_files),
                    "successful_years": merged_results["successful_years"],
                    "failed_years": [yr["year"] for yr in year_results if yr["processing_status"] == "failed"]
                },
                "multi_year_debt_equity_analysis": merged_results["merged_debt_equity_analysis"],
                "multi_year_financial_data": merged_results["merged_financial_data"],
                "detailed_year_results": year_results
            }
            
            # Step 5: Transform to final schema format
            print("\n🔄 STEP 5: Transforming to final schema format...")

            # Initialize transformer
            schema_transformer = FinalSchemaTransformer()

            # Transform results to your exact schema
            final_schema_output = schema_transformer.transform_to_final_schema({
                "merged_results": merged_results,
                "merged_debt_equity_analysis": merged_results["merged_debt_equity_analysis"],
                "merged_financial_data": merged_results["merged_financial_data"],
                "detailed_year_results": year_results,
                "plant_name": plant_name
            })

            # Save final schema output (THIS IS YOUR TARGET OUTPUT)
            final_schema_path = main_output_dir / f"{safe_plant_name}_final_schema_{self.timestamp}.json"

            with open(final_schema_path, 'w', encoding='utf-8') as f:
                json.dump(final_schema_output, f, indent=2, ensure_ascii=False, default=str)

            print(f"🎉 FINAL SCHEMA SAVED: {final_schema_path}")
            print(f"🌍 Detected Currency: {final_schema_output.get('currency', 'N/A')}")

            # Return the final schema output instead of comprehensive output
            return str(final_schema_path), final_schema_output

            # # Save comprehensive results
            # final_output_path = main_output_dir / f"{safe_plant_name}_comprehensive_multi_year_{self.timestamp}.json"
            
            # with open(final_output_path, 'w', encoding='utf-8') as f:
            #     json.dump(final_output, f, indent=2, ensure_ascii=False, default=str)
            
            # # Create summary
            # successful_count = len(merged_results["successful_years"])
            # total_count = len(pdf_files)
            
            # print(f"\n🎉 MULTI-YEAR PROCESSING COMPLETED!")
            # print(f"📁 Output directory: {main_output_dir}")
            # print(f"📄 Final results: {final_output_path}")
            # print(f"📊 Success rate: {successful_count}/{total_count} years")
            # print(f"✅ Successful years: {merged_results['successful_years']}")
            
            # if successful_count > 0:
            #     print(f"\n📈 Multi-Year Summary:")
            #     debt_data = merged_results["merged_debt_equity_analysis"]
            #     if debt_data:
            #         print(f"  • Debt-Equity data: {len(debt_data)} years")
            #         for year_data in debt_data:
            #             equity_pct = year_data.get('equity', {}).get('percentage')
            #             if equity_pct is not None:
            #                 print(f"    - {year_data.get('year', 'N/A')}: Equity {equity_pct*100:.1f}%")
                
            #     financial_data = merged_results["merged_financial_data"]
            #     print(f"  • Financial data:")
            #     for component, data in financial_data.items():
            #         if data:
            #             print(f"    - {component}: {len(data)} records")
            
            # return str(final_output_path), final_output
            
        except Exception as e:
            error_msg = f"Multi-year processing failed: {str(e)}"
            print(f"\n❌ ERROR: {error_msg}")
            logger.error(error_msg)
            
            results["error"] = error_msg
            
            return None, results

def main():
    """Main CLI interface for multi-year processing"""
    parser = argparse.ArgumentParser(description="Multi-Year Financial Document Processing")
    parser.add_argument("plant_name", help="Plant name (works for ANY plant globally)")
    parser.add_argument("--local-pdfs", nargs="+", help="Local PDF files to process")
    parser.add_argument("--years", nargs="+", type=int, help="Specific years to process (e.g., 2020 2021 2022)")
    
    args = parser.parse_args()
    
    print(f"🚀 Multi-Year Financial Processing Pipeline")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC") 
    print(f"👤 User: Gangatharan G")
    print(f"🏭 Plant: {args.plant_name}")
    print(f"🌍 Works for: ANY plant worldwide!")
    print(f"📆 Default years: 2020-2024 (5 years)")
    
    # Check environment
    required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("\nRequired setup:")
        for var in missing_vars:
            print(f"export {var}='your_api_key_here'")
        return
    
    try:
        # Initialize processor
        processor = MultiYearFinancialProcessor()
        
        # Run multi-year processing
        output_path, results = asyncio.run(
            processor.process_plant_multi_year(
                plant_name=args.plant_name,
                use_local_pdfs=args.local_pdfs,
                target_years=args.years
            )
        )
        
        if output_path:
            print(f"\n✅ Success! Multi-year results saved to: {output_path}")
        else:
            print(f"\n❌ Multi-year processing failed!")
            if 'error' in results:
                print(f"Error: {results['error']}")
        
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        logger.error(f"Fatal error: {e}")

if __name__ == "__main__":
    main()