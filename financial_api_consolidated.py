"""
Complete Financial Pipeline FastAPI Service with Consolidated Document Detection
Date: 2025-07-08 12:15:00 UTC
User: Gangatharan G
Purpose: FastAPI service for multi-year financial processing with consolidated document detection
Features: Web search + File upload modes, Background processing, S3 integration, Early stop for consolidated docs
"""

import os
import json
import asyncio
import uuid
import shutil
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Optional, Union
import tempfile
import traceback
import logging

# FastAPI and related imports
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# AWS S3
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

# Consolidated document detector
from consolidated_document_detector import check_single_consolidated_document

# Your existing pipeline
try:
    from multiyear_complete_integration_testing import CompleteIntegratedPipeline
    from process_output_file import (
        merge_powerplant_jsons, 
        create_final_output_with_static, 
        generate_final_output_package
    )
except ImportError as e:
    print(f"❌ Missing pipeline modules: {e}")
    print("Ensure multiyear_complete_integration_testing.py and process_output_file.py are available")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_pipeline_consolidated_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ===========================
# PYDANTIC MODELS
# ===========================

class ProcessWebSearchRequest(BaseModel):
    plant_name: str = Field(..., description="Name of the power plant")
    country_name: str = Field(..., description="Country name for S3 storage")
    entity_id: str = Field(..., description="Entity ID for S3 storage")
    target_years: Optional[List[int]] = Field(default=None, description="Specific years to process (e.g., [2020, 2021, 2022])")
    
    class Config:
        json_schema_extra = {
            "example": {
                "plant_name": "Adani Green Energy",
                "country_name": "India",
                "entity_id": "AGEL_001",
                "target_years": [2020, 2021, 2022, 2023, 2024]
            }
        }

class ConsolidatedCheckResult(BaseModel):
    is_consolidated: bool = False
    confidence: float = 0.0
    indicators: List[str] = []
    document_type: str = "STANDALONE"
    recommendation: str = "CONTINUE_PROCESSING"
    reasoning: str = ""

class JobStatusResponse(BaseModel):
    job_id: str
    status: str  # "processing", "completed", "failed", "stopped_consolidated"
    user: str
    timestamp: str
    progress: int = Field(default=0, ge=0, le=100)
    current_step: str = ""
    country_name: str = ""
    entity_id: str = ""
    plant_name: str = ""
    results: Optional[Dict] = None
    error: Optional[str] = None
    consolidated_check: Optional[ConsolidatedCheckResult] = None

class ProcessingResults(BaseModel):
    primary_output: Dict[str, str]
    assumptions_output: Optional[Dict[str, str]] = None
    static_data: Dict[str, str]
    processed_years: List[int]
    success_rate: str
    processing_summary: Dict

# ===========================
# GLOBAL VARIABLES
# ===========================

# In-memory job tracking (use Redis in production)
job_tracker: Dict[str, Dict] = {}

# S3 client
s3_client = None

# FastAPI app
app = FastAPI(
    title="Financial Pipeline API with Consolidated Detection",
    description="Multi-year financial processing with consolidated document detection",
    version="2.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===========================
# STARTUP AND CONFIGURATION
# ===========================

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global s3_client
    
    logger.info("🚀 Financial Pipeline API with Consolidated Detection Starting Up")
    logger.info(f"📅 Date: 2025-07-08 12:15:00 UTC")
    logger.info(f"👤 User: Gangatharan G")
    logger.info(f"📁 Source: final_output folder (STEP 5 output)")
    logger.info(f"🔍 Feature: Consolidated document detection")
    logger.info("="*80)
    
    # Validate environment variables
    required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        raise Exception(f"Missing required environment variables: {missing_vars}")
    
    # Initialize S3 client
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_REGION', 'us-east-1')
        )
        
        # Test S3 connection
        bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"✅ S3 connection successful - Bucket: {bucket_name}")
        
    except NoCredentialsError:
        logger.error("❌ AWS credentials not found")
        raise Exception("AWS credentials not configured")
    except ClientError as e:
        logger.error(f"❌ S3 connection failed: {e}")
        raise Exception(f"S3 connection failed: {e}")
    
    logger.info("✅ Financial Pipeline API Ready!")

# ===========================
# UTILITY FUNCTIONS
# ===========================

def get_current_utc_timestamp() -> str:
    """Get current UTC timestamp in YYYY-MM-DD HH:MM:SS format"""
    return datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")

def generate_job_id() -> str:
    """Generate unique job ID"""
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"job_{timestamp}_{unique_id}"

def update_job_status(job_id: str, status: str, progress: int = 0, 
                     current_step: str = "", results: Dict = None, error: str = None,
                     consolidated_check: Dict = None):
    """Update job status in tracker"""
    if job_id in job_tracker:
        job_tracker[job_id].update({
            "status": status,
            "progress": progress,
            "current_step": current_step,
            "last_updated": get_current_utc_timestamp()
        })
        
        if results:
            job_tracker[job_id]["results"] = results
        if error:
            job_tracker[job_id]["error"] = error
        if consolidated_check:
            job_tracker[job_id]["consolidated_check"] = consolidated_check
        
        logger.info(f"📊 Job {job_id}: {status} - {current_step} ({progress}%)")

async def upload_to_s3(file_path: str, s3_key: str, bucket_name: str = None) -> str:
    """Upload file to S3 and return URL"""
    if not bucket_name:
        bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
    
    try:
        # Upload file
        s3_client.upload_file(file_path, bucket_name, s3_key)
        
        # Generate URL
        s3_url = f"s3://{bucket_name}/{s3_key}"
        logger.info(f"✅ Uploaded to S3: {s3_url}")
        return s3_url
        
    except ClientError as e:
        logger.error(f"❌ S3 upload failed: {e}")
        raise Exception(f"S3 upload failed: {e}")

def validate_plant_name(plant_name: str) -> str:
    """Validate and sanitize plant name"""
    if not plant_name or len(plant_name.strip()) < 2:
        raise ValueError("Plant name must be at least 2 characters")
    
    # Basic sanitization for filenames
    sanitized = plant_name.strip().replace('/', '_').replace('\\', '_')
    return sanitized

def validate_years(years: List[int]) -> List[int]:
    """Validate target years"""
    current_year = datetime.now().year
    valid_years = []
    
    for year in years:
        if 2000 <= year <= current_year:
            valid_years.append(year)
        else:
            logger.warning(f"⚠️ Invalid year ignored: {year}")
    
    if not valid_years:
        raise ValueError("No valid years provided (must be between 2000 and current year)")
    
    return sorted(valid_years)

# ===========================
# S3 UPLOAD FUNCTIONS
# ===========================

async def upload_files_to_s3(plant_name: str, country_name: str, entity_id: str) -> Dict[str, str]:
    """
    Upload all result files to S3 - with smart directory detection
    """
    bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
    safe_plant_name = plant_name.replace(" ", "_").replace("/", "_")
    s3_prefix = f"{country_name}/{entity_id}"
    results = {}
    
    # Smart directory detection for final_output
    final_output_base = Path("final_output")
    final_output_dir = None
    
    logger.info(f"🔍 Looking for final_output directory for plant: '{plant_name}'")
    
    if final_output_base.exists():
        # List all directories in final_output for debugging
        existing_dirs = [d.name for d in final_output_base.iterdir() if d.is_dir()]
        logger.info(f"📁 Available final_output directories: {existing_dirs}")
        
        # Try multiple directory name patterns
        search_patterns = [
            plant_name,                                    # "Renew wind energy"
            plant_name.replace(" ", "_"),                  # "Renew_wind_energy"
            plant_name.replace(" ", "_").replace('"', ''), # Remove quotes
            safe_plant_name,                               # Sanitized version
        ]
        
        for pattern in search_patterns:
            test_dir = final_output_base / pattern
            logger.info(f"🔍 Trying pattern: '{pattern}' -> {test_dir}")
            
            if test_dir.exists():
                final_output_dir = test_dir
                logger.info(f"✅ Found final_output directory: {final_output_dir}")
                break
        
        # If exact matches fail, try fuzzy matching
        if not final_output_dir:
            # Normalize plant name for comparison
            normalized_plant = plant_name.lower().replace(" ", "").replace("_", "").replace("-", "")
            
            for existing_dir in final_output_base.iterdir():
                if existing_dir.is_dir():
                    normalized_existing = existing_dir.name.lower().replace(" ", "").replace("_", "").replace("-", "")
                    if normalized_plant == normalized_existing:
                        final_output_dir = existing_dir
                        logger.info(f"✅ Found final_output directory via fuzzy match: {final_output_dir}")
                        break
    
    if final_output_dir and final_output_dir.exists():
        logger.info("✅ Using final_output folder (STEP 5 completed)")
        return await upload_from_final_output(final_output_dir, s3_prefix, safe_plant_name, bucket_name)
    
    # FALLBACK: Use output_part2 folder if STEP 5 not found
    logger.warning("⚠️ final_output not found, falling back to output_part2 folder")
    
    # Find the most recent output_part2 directory
    output_part2_base = Path("output_part2")
    if not output_part2_base.exists():
        raise FileNotFoundError(f"Neither final_output nor output_part2 directories found")
    
    # Look for directories matching the plant name pattern
    plant_patterns = [
        plant_name.replace(" ", "_").replace('"', '').lower(),
        safe_plant_name.lower(),
        plant_name.lower().replace(" ", "_")
    ]
    
    matching_dirs = []
    for pattern in plant_patterns:
        matching_dirs.extend(list(output_part2_base.glob(f"{pattern}_complete_pipeline_*")))
    
    if not matching_dirs:
        # Try broader search
        matching_dirs = list(output_part2_base.glob(f"*_complete_pipeline_*"))
        logger.warning(f"⚠️ Using broader search, found: {[d.name for d in matching_dirs]}")
    
    if not matching_dirs:
        raise FileNotFoundError(f"No matching output directories found for plant: {plant_name}")
    
    # Use the most recent directory
    latest_dir = max(matching_dirs, key=lambda x: x.stat().st_mtime)
    logger.info(f"📁 Using fallback directory: {latest_dir}")
    
    return await upload_from_output_part2(latest_dir, s3_prefix, safe_plant_name, bucket_name, plant_name)

async def upload_from_final_output(final_output_dir: Path, s3_prefix: str, 
                                 safe_plant_name: str, bucket_name: str) -> Dict[str, str]:
    """Upload from final_output folder (preferred method)"""
    results = {}
    
    # Get all files for debugging
    all_files = list(final_output_dir.glob("*"))
    logger.info(f"📋 Files in final_output ({final_output_dir.name}): {[f.name for f in all_files]}")
    
    # Upload primary output - try multiple patterns
    primary_patterns = [
        "*financial_details.json",
        "*_financial_details.json",
        "*.json"  # Last resort
    ]
    
    primary_file = None
    for pattern in primary_patterns:
        primary_files = list(final_output_dir.glob(pattern))
        # Filter for financial details specifically
        primary_files = [f for f in primary_files if "financial_details" in f.name]
        if primary_files:
            primary_file = str(primary_files[0])
            break
    
    if primary_file:
        primary_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_details.json"
        results["primary_url"] = await upload_to_s3(primary_file, primary_s3_key, bucket_name)
        logger.info(f"✅ Uploaded primary: {primary_file}")
    else:
        # Try any JSON file as fallback
        json_files = list(final_output_dir.glob("*.json"))
        if json_files:
            primary_file = str(json_files[0])
            primary_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_details.json"
            results["primary_url"] = await upload_to_s3(primary_file, primary_s3_key, bucket_name)
            logger.info(f"✅ Uploaded primary (fallback): {primary_file}")
    
    # Upload assumptions output if available
    assumptions_patterns = [
        "*assumptions.json",
        "*_assumptions.json"
    ]
    
    assumptions_file = None
    for pattern in assumptions_patterns:
        assumptions_files = list(final_output_dir.glob(pattern))
        if assumptions_files:
            assumptions_file = str(assumptions_files[0])
            break
    
    if assumptions_file:
        assumptions_s3_key = f"{s3_prefix}/{safe_plant_name}_assumptions.json"
        results["assumptions_url"] = await upload_to_s3(assumptions_file, assumptions_s3_key, bucket_name)
        logger.info(f"✅ Uploaded assumptions: {assumptions_file}")
    
    # Upload static data if available
    static_patterns = [
        "*static_data.json",
        "*_static_data.json",
        "*static.json"
    ]
    
    static_file = None
    for pattern in static_patterns:
        static_files = list(final_output_dir.glob(pattern))
        if static_files:
            static_file = str(static_files[0])
            break
    
    if static_file:
        static_s3_key = f"{s3_prefix}/{safe_plant_name}_static_data.json"
        results["static_url"] = await upload_to_s3(static_file, static_s3_key, bucket_name)
        logger.info(f"✅ Uploaded static data: {static_file}")
    
    return results

async def upload_from_output_part2(output_dir: Path, s3_prefix: str, 
                                  safe_plant_name: str, bucket_name: str, 
                                  plant_name: str) -> Dict[str, str]:
    """Upload from output_part2 folder (fallback method)"""
    results = {}
    
    # Get all files for debugging
    all_files = list(output_dir.glob("*.json"))
    logger.info(f"📋 Files in output_part2 ({output_dir.name}): {[f.name for f in all_files]}")
    
    # Upload financial JSON
    financial_patterns = [
        f"{safe_plant_name}_financial.json",
        "*_financial.json",
        "*.json"  # Last resort
    ]
    
    financial_file = None
    for pattern in financial_patterns:
        financial_files = list(output_dir.glob(pattern))
        if financial_files:
            financial_file = str(financial_files[0])
            break
    
    if financial_file:
        financial_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_details.json"
        results["primary_url"] = await upload_to_s3(financial_file, financial_s3_key, bucket_name)
        logger.info(f"✅ Uploaded financial: {financial_file}")
    
    # Upload assumptions JSON
    assumptions_patterns = [
        f"{safe_plant_name}_financial_assumptions.json",
        "*_financial_assumptions.json",
        "*_assumptions.json"
    ]
    
    assumptions_file = None
    for pattern in assumptions_patterns:
        assumptions_files = list(output_dir.glob(pattern))
        if assumptions_files:
            assumptions_file = str(assumptions_files[0])
            break
    
    if assumptions_file:
        assumptions_s3_key = f"{s3_prefix}/{safe_plant_name}_assumptions.json"
        results["assumptions_url"] = await upload_to_s3(assumptions_file, assumptions_s3_key, bucket_name)
        logger.info(f"✅ Uploaded assumptions: {assumptions_file}")
    
    # For static data, we need to look in the outputs directory
    static_dir = Path("outputs") / plant_name
    if static_dir.exists():
        static_files = list(static_dir.glob("*static*.json"))
        if static_files:
            static_file = str(static_files[0])
            static_s3_key = f"{s3_prefix}/{safe_plant_name}_static_data.json"
            results["static_url"] = await upload_to_s3(static_file, static_s3_key, bucket_name)
            logger.info(f"✅ Uploaded static data: {static_file}")
    
    return results

async def upload_original_files_to_s3(uploaded_files: List[str], country_name: str, entity_id: str) -> Dict[str, str]:
    """Upload original PDF files to S3"""
    bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
    s3_prefix = f"{country_name}/{entity_id}/original_pdfs"
    results = {}
    
    for file_path in uploaded_files:
        file_name = Path(file_path).name
        s3_key = f"{s3_prefix}/{file_name}"
        
        try:
            s3_url = await upload_to_s3(file_path, s3_key, bucket_name)
            results[file_name] = s3_url
            logger.info(f"✅ Uploaded original PDF: {file_name}")
        except Exception as e:
            logger.error(f"❌ Failed to upload original PDF {file_name}: {e}")
    
    return results

# ===========================
# BACKGROUND TASKS
# ===========================

async def process_pipeline_websearch(job_id: str, plant_name: str, country_name: str, 
                                   entity_id: str, target_years: List[int] = None):
    """Background task for web search processing with consolidated document check"""
    try:
        update_job_status(job_id, "processing", 10, "Initializing pipeline...")
        
        # Initialize pipeline
        pipeline = CompleteIntegratedPipeline()
        
        # Get PDFs from web search
        logger.info(f"🔍 Getting PDFs for {plant_name} via web search...")
        pdf_files = await pipeline.get_pdfs(plant_name)
        
        if not pdf_files or len(pdf_files) == 0:
            raise Exception(f"No PDF files found for {plant_name} via web search")
        
        # Check first PDF for consolidated document
        if pdf_files:
            update_job_status(job_id, "processing", 15, "Checking for consolidated documents...")
            first_pdf = pdf_files[0]
            logger.info(f"🔍 Checking first PDF for consolidated indicators: {Path(first_pdf).name}")
            
            is_consolidated, detection_result = check_single_consolidated_document(first_pdf, confidence_threshold=0.7)
            
            # Update job status with detection results
            update_job_status(
                job_id, 
                "processing", 
                15, 
                "Consolidated document check completed",
                consolidated_check=detection_result
            )
            
            if is_consolidated:
                # Stop processing if consolidated document detected
                logger.warning(f"🛑 Job {job_id}: Stopping due to consolidated document")
                logger.warning(f"📊 Confidence: {detection_result['confidence']:.1%}")
                logger.warning(f"📋 Indicators: {', '.join(detection_result['indicators'])}")
                
                update_job_status(
                    job_id, 
                    "stopped_consolidated", 
                    0, 
                    "Consolidated document detected - processing stopped",
                    error="Processing stopped: Consolidated document detected",
                    consolidated_check=detection_result
                )
                return
        
        # Continue with normal processing if not consolidated
        update_job_status(job_id, "processing", 20, "Running web search and classification...")
        
        # Run complete pipeline with web search
        financial_path, assumptions_path, results = await pipeline.process_complete_pipeline(
            plant_name=plant_name,
            local_pdfs=pdf_files,  # Use web search
            target_years=target_years
        )
        
        if not financial_path:
            raise Exception("Pipeline processing failed - no financial data generated")
        
        update_job_status(job_id, "processing", 75, "Pipeline completed, STEP 5 generating final output...")
        
        # Wait for STEP 5 to complete (final_output generation)
        start_time = datetime.now()
        timeout = 300  # 5 minutes timeout
        final_output_dir = Path("final_output") / plant_name.replace(" ", "_").replace('"', '')
        
        while not final_output_dir.exists() and (datetime.now() - start_time).total_seconds() < timeout:
            logger.info(f"⏳ Waiting for final_output directory: {final_output_dir}")
            await asyncio.sleep(10)
            update_job_status(job_id, "processing", 80, "Waiting for final output generation...")
        
        # Calculate elapsed time
        elapsed_time = (datetime.now() - start_time).total_seconds()
        
        if not final_output_dir.exists():
            logger.warning(f"⚠️ STEP 5 timeout - proceeding with available files")
        
        update_job_status(job_id, "processing", 85, "Uploading final output to S3...")
        
        # Upload files from final_output folder to S3
        s3_results = await upload_files_to_s3(
            plant_name=plant_name,
            country_name=country_name,
            entity_id=entity_id
        )
        
        # Get successful years
        successful_years = results.get("merged_results", {}).get("successful_years", [])
        total_years = len(results.get("year_results", []))
        
        # Create final results
        final_results = {
            "primary_output": {
                "url": s3_results.get("primary_url", ""),
                "source": f"final_output/{plant_name}/"
            },
            "assumptions_output": {
                "url": s3_results.get("assumptions_url", ""),
                "source": f"final_output/{plant_name}/"
            },
            "static_data": {
                "url": s3_results.get("static_url", ""),
                "source": f"final_output/{plant_name}/"
            },
            "processed_years": successful_years,
            "success_rate": f"{len(successful_years)}/{total_years} years",
            "processing_summary": {
                "total_pdfs_processed": total_years,
                "successful_extractions": len(successful_years),
                "pipeline_mode": "websearch",
                "final_output_location": f"final_output/{plant_name}/",
                "step_5_completion_time": elapsed_time,
                "processing_time": get_current_utc_timestamp()
            }
        }
        
        update_job_status(job_id, "completed", 100, "Processing completed", results=final_results)
        logger.info(f"✅ Job {job_id} completed successfully")
        
    except Exception as e:
        error_msg = f"Pipeline processing failed: {str(e)}"
        logger.error(f"❌ Job {job_id} failed: {error_msg}")
        logger.error(traceback.format_exc())
        update_job_status(job_id, "failed", 0, "Processing failed", error=error_msg)

async def process_pipeline_upload(job_id: str, plant_name: str, country_name: str, 
                                entity_id: str, uploaded_files: List[str], target_years: List[int] = None):
    """Background task for file upload processing with consolidated document check"""
    try:
        update_job_status(job_id, "processing", 10, "Processing uploaded files...")
        
        # Check first PDF for consolidated document
        if uploaded_files:
            update_job_status(job_id, "processing", 15, "Checking for consolidated documents...")
            first_pdf = uploaded_files[0]
            logger.info(f"🔍 Checking first PDF for consolidated indicators: {Path(first_pdf).name}")
            
            is_consolidated, detection_result = check_single_consolidated_document(first_pdf, confidence_threshold=0.7)
            
            # Update job status with detection results
            update_job_status(
                job_id, 
                "processing", 
                15, 
                "Consolidated document check completed",
                consolidated_check=detection_result
            )
            
            if is_consolidated:
                # Stop processing if consolidated document detected
                logger.warning(f"🛑 Job {job_id}: Stopping due to consolidated document")
                logger.warning(f"📊 Confidence: {detection_result['confidence']:.1%}")
                logger.warning(f"📋 Indicators: {', '.join(detection_result['indicators'])}")
                
                update_job_status(
                    job_id, 
                    "stopped_consolidated", 
                    0, 
                    "Consolidated document detected - processing stopped",
                    error="Processing stopped: Consolidated document detected",
                    consolidated_check=detection_result
                )
                return
        
        # Initialize pipeline
        pipeline = CompleteIntegratedPipeline()
        update_job_status(job_id, "processing", 20, "Running classification on uploaded files...")
        
        # Run complete pipeline with uploaded files
        financial_path, assumptions_path, results = await pipeline.process_complete_pipeline(
            plant_name=plant_name,
            local_pdfs=uploaded_files,  # Use uploaded files
            target_years=target_years
        )
        
        if not financial_path:
            raise Exception("Pipeline processing failed - no financial data generated")
        
        update_job_status(job_id, "processing", 70, "Pipeline completed, STEP 5 generating final output...")
        
        # Wait for STEP 5 to complete (final_output generation)
        start_time = datetime.now()
        timeout = 300  # 5 minutes timeout
        final_output_dir = Path("final_output") / plant_name.replace(" ", "_").replace('"', '')
        
        while not final_output_dir.exists() and (datetime.now() - start_time).total_seconds() < timeout:
            logger.info(f"⏳ Waiting for final_output directory: {final_output_dir}")
            await asyncio.sleep(10)
            update_job_status(job_id, "processing", 75, "Waiting for final output generation...")
        
        # Calculate elapsed time
        elapsed_time = (datetime.now() - start_time).total_seconds()
        
        if not final_output_dir.exists():
            logger.warning(f"⚠️ STEP 5 timeout - proceeding with available files")
        
        # Upload original PDFs to S3 first
        await upload_original_files_to_s3(uploaded_files, country_name, entity_id)
        
        # Upload result files from final_output folder to S3
        s3_results = await upload_files_to_s3(
            plant_name=plant_name,
            country_name=country_name,
            entity_id=entity_id
        )
        
        # Get successful years
        successful_years = results.get("merged_results", {}).get("successful_years", [])
        total_years = len(results.get("year_results", []))
        
        # Create final results
        final_results = {
            "primary_output": {
                "url": s3_results.get("primary_url", ""),
                "source": f"final_output/{plant_name}/"
            },
            "assumptions_output": {
                "url": s3_results.get("assumptions_url", ""),
                "source": f"final_output/{plant_name}/"
            },
            "static_data": {
                "url": s3_results.get("static_url", ""),
                "source": f"final_output/{plant_name}/"
            },
            "processed_years": successful_years,
            "success_rate": f"{len(successful_years)}/{total_years} files",
            "processing_summary": {
                "total_pdfs_processed": total_years,
                "successful_extractions": len(successful_years),
                "pipeline_mode": "upload",
                "uploaded_files_count": len(uploaded_files),
                "final_output_location": f"final_output/{plant_name}/",
                "step_5_completion_time": elapsed_time,
                "processing_time": get_current_utc_timestamp()
            }
        }
        
        update_job_status(job_id, "completed", 100, "Processing completed", results=final_results)
        logger.info(f"✅ Job {job_id} completed successfully")
        
    except Exception as e:
        error_msg = f"Pipeline processing failed: {str(e)}"
        logger.error(f"❌ Job {job_id} failed: {error_msg}")
        logger.error(traceback.format_exc())
        update_job_status(job_id, "failed", 0, "Processing failed", error=error_msg)

# ===========================
# API ENDPOINTS
# ===========================

@app.get("/")
async def root():
    """API root endpoint"""
    return {
        "name": "Financial Pipeline API with Consolidated Detection",
        "version": "2.1.0",
        "date": "2025-07-08",
        "user": "Gangatharan G",
        "features": [
            "Web search mode",
            "File upload mode",
            "Consolidated document detection",
            "S3 integration",
            "Background processing"
        ],
        "docs": "/docs"
    }

@app.get("/api/status")
async def get_api_status():
    """Get API status"""
    return {
        "status": "online",
        "timestamp": get_current_utc_timestamp(),
        "version": "2.1.0",
        "jobs_count": len(job_tracker),
        "features": {
            "consolidated_detection": True,
            "s3_integration": True,
            "web_search": True,
            "file_upload": True
        }
    }

@app.get("/api/jobs/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """Get job status by ID"""
    if job_id not in job_tracker:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")
    
    job_data = job_tracker[job_id]
    
    return {
        "job_id": job_id,
        "status": job_data["status"],
        "user": job_data["user"],
        "timestamp": job_data["timestamp"],
        "progress": job_data["progress"],
        "current_step": job_data["current_step"],
        "country_name": job_data["country_name"],
        "entity_id": job_data["entity_id"],
        "plant_name": job_data["plant_name"],
        "results": job_data.get("results"),
        "error": job_data.get("error"),
        "consolidated_check": job_data.get("consolidated_check")
    }

@app.post("/api/process/websearch")
async def process_websearch(
    background_tasks: BackgroundTasks,
    request: ProcessWebSearchRequest
):
    """Process plant using web search"""
    try:
        # Validate inputs
        plant_name = validate_plant_name(request.plant_name)
        country_name = request.country_name
        entity_id = request.entity_id
        
        # Validate target years if provided
        target_years_list = None
        if request.target_years:
            target_years_list = validate_years(request.target_years)
        
        # Generate job ID
        job_id = generate_job_id()
        
        # Initialize job status
        job_tracker[job_id] = {
            "status": "processing",
            "progress": 0,
            "current_step": "Initializing web search...",
            "timestamp": get_current_utc_timestamp(),
            "last_updated": get_current_utc_timestamp(),
            "user": "Gangatharan G",
            "country_name": country_name,
            "entity_id": entity_id,
            "plant_name": plant_name,
            "mode": "websearch",
            "target_years": target_years_list,
            "results": None,
            "error": None,
            "source": "final_output folder",
            "consolidated_check": {
                "is_consolidated": False,
                "confidence": 0.0,
                "indicators": [],
                "document_type": "STANDALONE",
                "recommendation": "CONTINUE_PROCESSING",
                "reasoning": ""
            }
        }
        
        # Start background task
        background_tasks.add_task(
            process_pipeline_websearch,
            job_id, plant_name, country_name, entity_id, target_years_list
        )
        
        logger.info(f"🚀 Started web search job {job_id} for {plant_name}")
        
        return JSONResponse({
            "job_id": job_id,
            "status": "processing",
            "user": "Gangatharan G",
            "timestamp": get_current_utc_timestamp(),
            "message": f"Pipeline started for {plant_name} with web search",
            "estimated_time": "15-30 minutes",
            "mode": "websearch",
            "country_name": country_name,
            "entity_id": entity_id,
            "target_years": target_years_list,
            "source": "final_output folder (STEP 5)"
        })
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Error starting web search job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start processing: {str(e)}")

@app.post("/api/process/upload")
async def process_upload_files(
    background_tasks: BackgroundTasks,
    plant_name: str = Form(...),
    country_name: str = Form(...),
    entity_id: str = Form(...),
    files: List[UploadFile] = File(...),
    target_years: Optional[str] = Form(None)
):
    """Process uploaded PDF files"""
    try:
        # Validate inputs
        plant_name = validate_plant_name(plant_name)
        
        # Parse target years if provided
        target_years_list = None
        if target_years:
            try:
                target_years_list = [int(y.strip()) for y in target_years.split(",") if y.strip()]
                target_years_list = validate_years(target_years_list)
            except ValueError as e:
                raise ValueError(f"Invalid target years: {e}")
        
        # Validate files
        if not files or len(files) == 0:
            raise ValueError("No files uploaded")
        
        # Check file types
        pdf_files = []
        for file in files:
            if not file.filename.lower().endswith('.pdf'):
                raise ValueError(f"Invalid file type: {file.filename}. Only PDF files are allowed.")
            pdf_files.append(file)
        
        if len(pdf_files) > 10:  # Reasonable limit
            raise ValueError("Too many files. Maximum 10 PDF files allowed.")
        
        # Generate job ID
        job_id = generate_job_id()
        
        # Save uploaded files temporarily
        temp_dir = Path(tempfile.mkdtemp(prefix=f"upload_{job_id}_"))
        saved_files = []
        
        for file in pdf_files:
            temp_file_path = temp_dir / file.filename
            with open(temp_file_path, "wb") as temp_file:
                content = await file.read()
                temp_file.write(content)
            saved_files.append(str(temp_file_path))
        
        # Initialize job status
        job_tracker[job_id] = {
            "status": "processing",
            "progress": 0,
            "current_step": "Processing uploaded files...",
            "timestamp": get_current_utc_timestamp(),
            "last_updated": get_current_utc_timestamp(),
            "user": "Gangatharan G",
            "country_name": country_name,
            "entity_id": entity_id,
            "plant_name": plant_name,
            "mode": "upload",
            "target_years": target_years_list,
            "uploaded_files": [f.filename for f in pdf_files],
            "results": None,
            "error": None,
            "source": "final_output folder",
            "consolidated_check": {
                "is_consolidated": False,
                "confidence": 0.0,
                "indicators": [],
                "document_type": "STANDALONE",
                "recommendation": "CONTINUE_PROCESSING",
                "reasoning": ""
            }
        }
        
        # Start background task
        background_tasks.add_task(
            process_pipeline_upload,
            job_id, plant_name, country_name, entity_id, saved_files, target_years_list
        )
        
        logger.info(f"🚀 Started upload job {job_id} for {plant_name} with {len(pdf_files)} files")
        
        return JSONResponse({
            "job_id": job_id,
            "status": "processing",
            "user": "Gangatharan G",
            "timestamp": get_current_utc_timestamp(),
            "message": f"Pipeline started for {plant_name} with {len(pdf_files)} uploaded files",
            "estimated_time": "10-25 minutes",
            "mode": "upload",
            "country_name": country_name,
            "entity_id": entity_id,
            "uploaded_files": [f.filename for f in pdf_files],
            "target_years": target_years_list,
            "source": "final_output folder (STEP 5)"
        })
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Error starting upload job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start processing: {str(e)}")

# ===========================
# MAIN ENTRY POINT
# ===========================

if __name__ == "__main__":
    uvicorn.run("financial_api_consolidated:app", host="0.0.0.0", port=8000, reload=True)