# import os
# import json
# import re
# from pathlib import Path
# from datetime import datetime

# from web_search import run_downloader
# from classify import EnhancedPDFFinancialClassifier
# from prompt_scehma_new import get_dynamic_prompts
# from financial_data_pipeline_new import extract_consolidated_financials

# def extract_year_from_filename(pdf_path: str) -> int:
#     match = re.search(r'20\d{2}', Path(pdf_path).name)
#     return int(match.group()) if match else datetime.now().year

# def find_classified_image(output_dir: Path, classification_type: str) -> str:
#     dir_map = {
#         "balance_sheet": "statement_of_balance_sheet",
#         "profit_loss": "statement_of_profit_and_loss",
#         "cash_flows": "statement_of_cash_flows",
#         "credit_rating": "credit_rating"
#     }
#     target = output_dir / "classified_images" / dir_map[classification_type]
#     return str(next(target.glob("*.png"), None)) if target.exists() else None

# def process_pdf(pdf_file: str, output_dir: Path) -> dict:
#     year = extract_year_from_filename(pdf_file)
#     year_dir = output_dir / f"year_{year}"
#     year_dir.mkdir(parents=True, exist_ok=True)

#     # Step 1: Classify PDF
#     classifier = EnhancedPDFFinancialClassifier(batch_size=5)
#     classifier.classify_pdf_batch_by_batch(pdf_path=pdf_file, output_dir=str(year_dir))

#     # Step 2: Find classified images
#     bs = find_classified_image(year_dir, "balance_sheet")
#     pl = find_classified_image(year_dir, "profit_loss")
#     cf = find_classified_image(year_dir, "cash_flows")
#     cr = find_classified_image(year_dir, "credit_rating")

#     if not any([bs, pl, cf, cr]):
#         return {"error": "No classified financial images found", "year": year}

#     # Fallbacks
#     bs = bs or pl or cf
#     pl = pl or bs or cf
#     cf = cf or bs or pl
#     cr = cr or bs

#     # Step 3: Extract using prompts
#     prompts = get_dynamic_prompts(year)
#     output_json_path = year_dir / f"financial_data_{year}.json"

#     extract_consolidated_financials(
#         balance_sheet_path=bs,
#         profit_and_loss_path=pl,
#         cash_flows_path=cf,
#         credit_rating_path=cr,
#         assets_prompt=prompts["assets_prompt"],
#         cashflows_prompt=prompts["cashflows_prompt"],
#         revenue_expenses_prompt=prompts["revenue_expenses_prompt"],
#         credit_rating_prompt=prompts["credit_rating_prompt"],
#         output_file_path=str(output_json_path)
#     )

#     with open(output_json_path, 'r') as f:
#         return json.load(f)

# def main(plant_name: str, years=[2020, 2021, 2022, 2023, 2024]):
#     print(f"📥 Downloading reports for {plant_name}...")
#     run_downloader(plant_name, min(years), max(years))

#     safe_name = plant_name.replace(" ", "_").replace('"', '')
#     pdf_dir = Path("downloads") / safe_name
#     output_dir = Path(f"{safe_name}_extracted_output")
#     output_dir.mkdir(exist_ok=True)

#     result_summary = {}

#     for pdf_file in pdf_dir.glob("*.pdf"):
#         year = extract_year_from_filename(str(pdf_file))
#         if year in years:
#             print(f"🔍 Processing year {year}: {pdf_file.name}")
#             result = process_pdf(str(pdf_file), output_dir)
#             result_summary[year] = result

#     # Save summary JSON
#     summary_path = output_dir / f"{safe_name}_financial_summary.json"
#     with open(summary_path, 'w') as f:
#         json.dump(result_summary, f, indent=2)

#     print(f"\n✅ Extraction complete. Summary saved at: {summary_path}")

# if __name__ == "__main__":
#     main("RenewAkshayUrjaLimited")  # Replace with your plant name


from web_search import run_downloader



run_downloader()