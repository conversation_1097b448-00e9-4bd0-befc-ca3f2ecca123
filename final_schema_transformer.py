"""
Final Schema Transformer - Worldwide Currency Support
Date: 2025-06-30 11:18:05 UTC
User: Gangatharan G
Purpose: Transform multi-year results to exact target schema with global currency detection
"""

import json
from typing import Dict, List, Any, Optional
from pathlib import Path

class FinalSchemaTransformer:
    """Transform integration output to exact target schema format"""
    
    # 🌍 WORLDWIDE CURRENCY MAPPING
    CURRENCY_MAPPING = {
        'INR': 'INR',           # Indian Rupee
        'USD': 'USD',           # US Dollar
        'EUR': 'EUR',           # Euro
        'GBP': 'GBP',           # British Pound
        'ZAR': 'ZAR',           # South African Rand
        'RAND': 'ZAR',          # South African Rand (alternative)
        'AUD': 'AUD',           # Australian Dollar
        'CAD': 'CAD',           # Canadian Dollar
        'JPY': 'JPY',           # Japanese Yen
        'CNY': 'CNY',           # Chinese Yuan
        'BRL': 'BRL',           # Brazilian Real
        'MXN': 'MXN',           # Mexican Peso
        'KRW': 'KRW',           # South Korean Won
        'SGD': 'SGD',           # Singapore Dollar
        'HKD': 'HKD',           # Hong Kong Dollar
        'CHF': 'CHF',           # Swiss Franc
        'SEK': 'SEK',           # Swedish Krona
        'NOK': 'NOK',           # Norwegian Krone
        'DKK': 'DKK',           # Danish Krone
        'PLN': 'PLN',           # Polish Zloty
        'RUB': 'RUB',           # Russian Ruble
        'TRY': 'TRY',           # Turkish Lira
        'THB': 'THB',           # Thai Baht
        'MYR': 'MYR',           # Malaysian Ringgit
        'IDR': 'IDR',           # Indonesian Rupiah
        'PHP': 'PHP',           # Philippine Peso
        'VND': 'VND',           # Vietnamese Dong
        'NZD': 'NZD',           # New Zealand Dollar
        'CLP': 'CLP',           # Chilean Peso
        'COP': 'COP',           # Colombian Peso
        'PEN': 'PEN',           # Peruvian Sol
        'EGP': 'EGP',           # Egyptian Pound
        'NGN': 'NGN',           # Nigerian Naira
        'KES': 'KES',           # Kenyan Shilling
        'GHS': 'GHS',           # Ghanaian Cedi
        'MAD': 'MAD',           # Moroccan Dirham
        'QAR': 'QAR',           # Qatari Riyal
        'AED': 'AED',           # UAE Dirham
        'SAR': 'SAR',           # Saudi Riyal
        'KWD': 'KWD',           # Kuwaiti Dinar
        'BHD': 'BHD',           # Bahraini Dinar
        'OMR': 'OMR',           # Omani Rial
        'ILS': 'ILS',           # Israeli Shekel
        'CZK': 'CZK',           # Czech Koruna
        'HUF': 'HUF',           # Hungarian Forint
        'RON': 'RON',           # Romanian Leu
        'BGN': 'BGN',           # Bulgarian Lev
        'HRK': 'HRK',           # Croatian Kuna
        'ISK': 'ISK',           # Icelandic Krona
        'LKR': 'LKR',           # Sri Lankan Rupee
        'PKR': 'PKR',           # Pakistani Rupee
        'BDT': 'BDT',           # Bangladeshi Taka
        'NPR': 'NPR',           # Nepalese Rupee
        'AFN': 'AFN',           # Afghan Afghani
        'MMK': 'MMK',           # Myanmar Kyat
        'LAK': 'LAK',           # Lao Kip
        'KHR': 'KHR',           # Cambodian Riel
        'TWD': 'TWD',           # Taiwan Dollar
        'MNT': 'MNT'            # Mongolian Tugrik
    }
    
    # 🌍 CURRENCY UNIT MAPPINGS
    CURRENCY_UNITS = {
        'INR': {
            'crore': 10000000,
            'crores': 10000000,
            'lakh': 100000,
            'lakhs': 100000
        },
        'USD': {
            'million': 1000000,
            'millions': 1000000,
            'billion': 1000000000,
            'billions': 1000000000
        },
        'EUR': {
            'million': 1000000,
            'millions': 1000000,
            'billion': 1000000000,
            'billions': 1000000000
        },
        'ZAR': {
            'million': 1000000,
            'millions': 1000000,
            'rm': 1000000,
            'rand_millions': 1000000
        },
        'GBP': {
            'million': 1000000,
            'millions': 1000000,
            'billion': 1000000000,
            'billions': 1000000000
        }
    }
    
    def __init__(self):
        self.detected_currency = "INR"  # Default fallback
        self.detected_unit = "lakhs"    # Default fallback
        
    def detect_currency_from_results(self, multi_year_results: Dict) -> str:
        """🌍 Detect currency from multi-year results - works worldwide"""
        
        # Method 1: Check if currency was detected during processing
        merged_financial = multi_year_results.get("merged_financial_data", {})
        
        # Look for currency in credit rating data
        credit_ratings = merged_financial.get("credit_rating", [])
        for rating in credit_ratings:
            if isinstance(rating, dict) and "currency" in rating:
                currency = rating["currency"].upper()
                if currency in self.CURRENCY_MAPPING:
                    return self.CURRENCY_MAPPING[currency]
        
        # Method 2: Check detailed year results for currency detection
        year_results = multi_year_results.get("detailed_year_results", [])
        for year_result in year_results:
            financial_data = year_result.get("financial_data", {})
            if isinstance(financial_data, dict) and "currency" in financial_data:
                currency = financial_data["currency"].upper()
                if currency in self.CURRENCY_MAPPING:
                    return self.CURRENCY_MAPPING[currency]
        
        # Method 3: Analyze plant name for geographic hints
        plant_name = multi_year_results.get("plant_name", "").lower()
        
        # Geographic currency detection
        geographic_hints = {
            'india': 'INR', 'indian': 'INR', 'mumbai': 'INR', 'delhi': 'INR', 'bangalore': 'INR',
            'usa': 'USD', 'america': 'USD', 'us': 'USD', 'california': 'USD', 'texas': 'USD',
            'south africa': 'ZAR', 'johannesburg': 'ZAR', 'cape town': 'ZAR', 'durban': 'ZAR',
            'uk': 'GBP', 'britain': 'GBP', 'england': 'GBP', 'london': 'GBP', 'scotland': 'GBP',
            'germany': 'EUR', 'france': 'EUR', 'spain': 'EUR', 'italy': 'EUR', 'netherlands': 'EUR',
            'australia': 'AUD', 'sydney': 'AUD', 'melbourne': 'AUD', 'perth': 'AUD',
            'canada': 'CAD', 'toronto': 'CAD', 'vancouver': 'CAD', 'montreal': 'CAD',
            'japan': 'JPY', 'tokyo': 'JPY', 'osaka': 'JPY', 'kyoto': 'JPY',
            'china': 'CNY', 'beijing': 'CNY', 'shanghai': 'CNY', 'guangzhou': 'CNY',
            'brazil': 'BRL', 'sao paulo': 'BRL', 'rio': 'BRL', 'brasilia': 'BRL',
            'mexico': 'MXN', 'mexico city': 'MXN', 'guadalajara': 'MXN',
            'korea': 'KRW', 'seoul': 'KRW', 'busan': 'KRW',
            'singapore': 'SGD', 'hong kong': 'HKD', 'thailand': 'THB', 'bangkok': 'THB',
            'malaysia': 'MYR', 'kuala lumpur': 'MYR', 'indonesia': 'IDR', 'jakarta': 'IDR',
            'philippines': 'PHP', 'manila': 'PHP', 'vietnam': 'VND', 'ho chi minh': 'VND',
            'new zealand': 'NZD', 'auckland': 'NZD', 'wellington': 'NZD',
            'chile': 'CLP', 'santiago': 'CLP', 'colombia': 'COP', 'bogota': 'COP',
            'peru': 'PEN', 'lima': 'PEN', 'egypt': 'EGP', 'cairo': 'EGP',
            'nigeria': 'NGN', 'lagos': 'NGN', 'kenya': 'KES', 'nairobi': 'KES',
            'ghana': 'GHS', 'accra': 'GHS', 'morocco': 'MAD', 'casablanca': 'MAD',
            'qatar': 'QAR', 'doha': 'QAR', 'uae': 'AED', 'dubai': 'AED', 'abu dhabi': 'AED',
            'saudi': 'SAR', 'riyadh': 'SAR', 'kuwait': 'KWD', 'bahrain': 'BHD',
            'oman': 'OMR', 'muscat': 'OMR', 'israel': 'ILS', 'tel aviv': 'ILS',
            'poland': 'PLN', 'warsaw': 'PLN', 'russia': 'RUB', 'moscow': 'RUB',
            'turkey': 'TRY', 'istanbul': 'TRY', 'ankara': 'TRY'
        }
        
        for hint, currency in geographic_hints.items():
            if hint in plant_name:
                return currency
        
        # Default fallback
        return "INR"
    
    def create_empty_comparable_companies(self) -> Dict:
        """Create empty comparable companies structure"""
        return {
            "ebit": {
                "pref_method": "",
                "value": None
            },
            "ebitda": {
                "pref_method": "",
                "value": None
            },
            "ebitda_weight": {
                "leading": None,
                "trailing": None
            },
            "ebit_weight": {
                "leading": None,
                "trailing": None
            },
            "investor_details": [
                {
                    "ebitda_ltm": None,
                    "ebitda_ntm": None,
                    "ebit_ltm": None,
                    "ebit_ntm": None,
                    "enterprise_value": None,
                    "name": "",
                    "revenue_ltm": None,
                    "revenue_ntm": None
                }
            ],
            "revenue": {
                "pref_method": "",
                "value": None
            },
            "revenue_weight": {
                "leading": None,
                "trailing": None
            },
            "selected_method": ""
        }
    
    def create_empty_market_price(self) -> Dict:
        """Create empty market price structure"""
        return {
            "currency": "",
            "day_count": None,
            "total_day_count": [],
            "valuation_date": "",
            "values": []
        }
    
    def create_empty_valuation_method(self) -> Dict:
        """Create empty valuation method structure"""
        return {
            "discounted_cashflow_method": {
                "equity_value": None,
                "value_per_share": None,
                "weight": None
            },
            "net_asset_value_method": {
                "equity_value": None,
                "value_per_share": None,
                "weight": None
            }
        }
    
    def extract_latest_assets(self, multi_year_results: Dict, latest_year: int) -> Dict:
        """Extract assets for latest year only"""
        merged_financial = multi_year_results.get("merged_financial_data", {})
        assets_list = merged_financial.get("assets", [])
        
        # Find assets for latest year
        for assets_data in assets_list:
            if isinstance(assets_data, dict) and assets_data.get("year") == latest_year:
                # Remove year field and return assets structure
                assets_clean = assets_data.copy()
                assets_clean.pop("year", None)
                return assets_clean
        
        # Fallback: return first assets if latest year not found
        if assets_list and isinstance(assets_list[0], dict):
            assets_clean = assets_list[0].copy()
            assets_clean.pop("year", None)
            return assets_clean
        
        # Empty fallback
        return {
            "current_assets": {
                "asset_type": [
                    {"name": "cash_and_cash_equivalents", "value": None},
                    {"name": "inventory", "value": None},
                    {"name": "other_assets", "value": None},
                    {"name": "trade_receivable", "value": None}
                ]
            },
            "date": "",
            "other_non_current_assets": {
                "asset_type": [
                    {"name": "capital_work_in_progress", "value": None},
                    {"name": "intangible_assets", "value": None},
                    {"name": "financial_assets", "value": None},
                    {"name": "other_non_current_assets", "value": None},
                    {"name": "income_tax_assets_net", "value": None}
                ]
            },
            "property_plant_equipment": {
                "asset_type": [
                    {"name": "property_plant_equipment", "value": None}
                ]
            }
        }
    
    def extract_latest_equity_liability(self, multi_year_results: Dict, latest_year: int) -> Dict:
        """Extract equity_liability for latest year only"""
        merged_debt_equity = multi_year_results.get("merged_debt_equity_analysis", [])
        
        # Find detailed equity liability data for latest year
        year_results = multi_year_results.get("detailed_year_results", [])
        for year_result in year_results:
            if year_result.get("year") == latest_year and year_result.get("processing_status") == "completed":
                debt_equity_data = year_result.get("debt_equity_analysis", {})
                if debt_equity_data and "equity_liability" in debt_equity_data:
                    return debt_equity_data["equity_liability"]
        
        # Empty fallback
        return {
            "date": "",
            "equity": {
                "equity_share_capital": [
                    {
                        "name": "",
                        "no_of_shares": None,
                        "value": None
                    }
                ],
                "other_equity": [
                    {
                        "name": "",
                        "value": None
                    }
                ],
                "roe": None
            },
            "long_term_debt": {
                "interest": "",
                "junior_debt": {
                    "investor_details": [
                        {
                            "name": "",
                            "type": "",
                            "value": None
                        }
                    ],
                    "value": None
                },
                "senior_debt": {
                    "investor_details": [
                        {
                            "name": "",
                            "type": "",
                            "value": None
                        }
                    ],
                    "value": None
                }
            },
            "others": {
                "derivative_financial_instruments": [
                    {
                        "name": "",
                        "value": None
                    }
                ],
                "others": [
                    {
                        "name": "",
                        "value": None
                    }
                ]
            },
            "short_term_debt": {
                "interest": "",
                "junior_debt": {
                    "investor_details": [
                        {
                            "name": "",
                            "type": "",
                            "value": None
                        }
                    ],
                    "value": None
                },
                "senior_debt": {
                    "investor_details": [
                        {
                            "name": "",
                            "type": "",
                            "value": None
                        }
                    ],
                    "value": None
                }
            }
        }
    
    def extract_multi_year_data(self, multi_year_results: Dict, field_name: str) -> List:
        """Extract multi-year data for cashflows, credit_rating, revenue_expenses"""
        merged_financial = multi_year_results.get("merged_financial_data", {})
        return merged_financial.get(field_name, [])
    
    def extract_debt_equity_analysis(self, multi_year_results: Dict) -> List:
        """Extract multi-year debt equity analysis"""
        return multi_year_results.get("merged_debt_equity_analysis", [])
    
    def extract_credit_rating_note(self, multi_year_results: Dict) -> str:
        """Extract credit rating note from financial data"""
        merged_financial = multi_year_results.get("merged_financial_data", {})
        credit_ratings = merged_financial.get("credit_rating", [])
        
        for rating in credit_ratings:
            if isinstance(rating, dict) and "credit_rating_note" in rating:
                return rating["credit_rating_note"]
        
        # Check detailed year results
        year_results = multi_year_results.get("detailed_year_results", [])
        for year_result in year_results:
            financial_data = year_result.get("financial_data", {})
            if isinstance(financial_data, dict) and "credit_rating_note" in financial_data:
                return financial_data["credit_rating_note"]
        
        return ""
    
    def transform_to_final_schema(self, multi_year_results: Dict) -> Dict:
        """🌍 Transform to final schema with worldwide currency support"""
        
        print("\n🔄 Transforming to final schema format...")
        
        # Detect currency dynamically
        detected_currency = self.detect_currency_from_results(multi_year_results)
        self.detected_currency = detected_currency
        
        print(f"🌍 Detected currency: {detected_currency}")
        
        # Find latest year
        successful_years = multi_year_results.get("merged_results", {}).get("successful_years", [])
        if not successful_years:
            successful_years = [year_result.get("year", 2024) for year_result in multi_year_results.get("detailed_year_results", [])]
        
        latest_year = max(successful_years) if successful_years else 2024
        print(f"📅 Latest year: {latest_year}")
        
        # Extract data
        assets_latest = self.extract_latest_assets(multi_year_results, latest_year)
        equity_liability_latest = self.extract_latest_equity_liability(multi_year_results, latest_year)
        
        cashflows = self.extract_multi_year_data(multi_year_results, "cashflows")
        credit_rating = self.extract_multi_year_data(multi_year_results, "credit_rating")
        revenue_expenses = self.extract_multi_year_data(multi_year_results, "revenue_expenses")
        debt_equity_analysis = self.extract_debt_equity_analysis(multi_year_results)
        
        credit_rating_note = self.extract_credit_rating_note(multi_year_results)
        
        # Build final schema
        final_schema = {
            # Core fields with data
            "assets": assets_latest,
            "cashflows": cashflows,
            "credit_rating": credit_rating,
            "debt_equity_analysis": debt_equity_analysis,
            "equity_liability": equity_liability_latest,
            "revenue_expenses": revenue_expenses,
            
            # Metadata fields
            "pk": "",
            "sk": "financials",
            "amount_in": detected_currency,
            "credit_rating_note": credit_rating_note,
            "currency": detected_currency,
            
            # Empty template fields
            "comparable_companies": self.create_empty_comparable_companies(),
            "exchange": "",
            "listed": True,
            "market_price": self.create_empty_market_price(),
            "preferred_valuation_method": self.create_empty_valuation_method(),
            "ticker": ""
        }
        
        print(f"✅ Final schema transformation completed")
        print(f"🌍 Currency: {detected_currency}")
        print(f"📊 Data fields: {len([k for k, v in final_schema.items() if v and k not in ['pk', 'sk', 'amount_in', 'currency', 'exchange', 'listed', 'ticker']])}/6 populated")
        
        return final_schema


def add_final_schema_transformer_to_integration():
    """Show how to integrate the transformer into the main pipeline"""
    
    integration_code = '''
# Add this to your multi_year_simple_integration.py

# Import the transformer
from final_schema_transformer import FinalSchemaTransformer

# In the process_plant_multi_year method, after Step 4:

# Step 5: Transform to final schema
print("\\n🔄 STEP 5: Transforming to final schema format...")
results["processing_steps"].append("Step 5: Final Schema Transformation")

# Initialize transformer
schema_transformer = FinalSchemaTransformer()

# Transform to final schema
final_schema_output = schema_transformer.transform_to_final_schema(results)

# Save final schema output
final_schema_path = main_output_dir / f"{safe_plant_name}_final_schema_{self.timestamp}.json"

with open(final_schema_path, 'w', encoding='utf-8') as f:
    json.dump(final_schema_output, f, indent=2, ensure_ascii=False, default=str)

print(f"🎉 FINAL SCHEMA OUTPUT SAVED: {final_schema_path}")
print(f"🌍 Currency: {final_schema_output.get('currency', 'N/A')}")

return str(final_schema_path), final_schema_output
'''
    
    return integration_code


# Example usage
if __name__ == "__main__":
    print("🌍 Final Schema Transformer - Worldwide Currency Support")
    print("="*60)
    print("Supported currencies:", len(FinalSchemaTransformer.CURRENCY_MAPPING))
    print("Geographic detection: Enabled")
    print("Dynamic currency detection: Enabled")
    print("\nReady to transform multi-year results to final schema format!")