# financial_model_new.py: 
import openai
import base64
from PIL import Image
from io import BytesIO
from dotenv import load_dotenv
import os

load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")



# Set your OpenAI API key
client = openai.OpenAI(api_key=api_key)

def encode_image_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

def ask_gpt4o_vision(image_paths, question):
    image_messages = [
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/png;base64,{encode_image_base64(path)}"
            }
        }
        for path in image_paths
    ]

    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": question},
                    *image_messages
                ]
            }
        ],
        max_tokens=2000,
    )

    return response.choices[0].message.content
