"""
Complete Financial Pipeline FastAPI Service
Date: 2025-07-07 11:06:00 UTC
User: Gangatharangurusamy
Purpose: FastAPI service for multi-year financial processing with S3 storage
Features: Web search + File upload modes, Background processing, S3 integration
Updated: Uses final_output folder from STEP 5 of pipeline
"""

import os
import json
import asyncio
import uuid
import shutil
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Optional, Union
import tempfile
import traceback
import logging

# FastAPI and related imports
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# AWS S3
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

# Your existing pipeline
try:
    from multiyear_complete_integration_testing import CompleteIntegratedPipeline
    from process_output_file import (
        merge_powerplant_jsons, 
        create_final_output_with_static, 
        generate_final_output_package
    )
except ImportError as e:
    print(f"❌ Missing pipeline modules: {e}")
    print("Ensure multiyear_complete_integration_testing.py and process_output_file.py are available")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_pipeline_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ===========================
# PYDANTIC MODELS
# ===========================

class ProcessWebSearchRequest(BaseModel):
    plant_name: str = Field(..., description="Name of the power plant")
    country_name: str = Field(..., description="Country name for S3 storage")
    entity_id: str = Field(..., description="Entity ID for S3 storage")
    target_years: Optional[List[int]] = Field(default=None, description="Specific years to process (e.g., [2020, 2021, 2022])")
    
    class Config:
        json_schema_extra = {
            "example": {
                "plant_name": "Adani Green Energy",
                "country_name": "India",
                "entity_id": "AGEL_001",
                "target_years": [2020, 2021, 2022, 2023, 2024]
            }
        }

class JobStatusResponse(BaseModel):
    job_id: str
    status: str  # "processing", "completed", "failed"
    user: str
    timestamp: str
    progress: int = Field(default=0, ge=0, le=100)
    current_step: str = ""
    country_name: str = ""
    entity_id: str = ""
    plant_name: str = ""
    results: Optional[Dict] = None
    error: Optional[str] = None

class ProcessingResults(BaseModel):
    primary_output: Dict[str, str]
    assumptions_output: Optional[Dict[str, str]] = None
    static_data: Dict[str, str]
    processed_years: List[int]
    success_rate: str
    processing_summary: Dict

# ===========================
# GLOBAL VARIABLES
# ===========================

# In-memory job tracking (use Redis in production)
job_tracker: Dict[str, Dict] = {}

# S3 client
s3_client = None

# FastAPI app
app = FastAPI(
    title="Financial Pipeline API",
    description="Multi-year financial processing with S3 storage from final_output folder",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===========================
# STARTUP AND CONFIGURATION
# ===========================

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global s3_client
    
    logger.info("🚀 Financial Pipeline API Starting Up")
    logger.info(f"📅 Date: 2025-07-07 11:06:00 UTC")
    logger.info(f"👤 User: Gangatharangurusamy")
    logger.info(f"📁 Source: final_output folder (STEP 5 output)")
    logger.info("="*80)
    
    # Validate environment variables
    required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {missing_vars}")
        raise Exception(f"Missing required environment variables: {missing_vars}")
    
    # Initialize S3 client
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_REGION', 'us-east-1')
        )
        
        # Test S3 connection
        bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"✅ S3 connection successful - Bucket: {bucket_name}")
        
    except NoCredentialsError:
        logger.error("❌ AWS credentials not found")
        raise Exception("AWS credentials not configured")
    except ClientError as e:
        logger.error(f"❌ S3 connection failed: {e}")
        raise Exception(f"S3 connection failed: {e}")
    
    logger.info("✅ Financial Pipeline API Ready!")

# ===========================
# UTILITY FUNCTIONS
# ===========================

def get_current_utc_timestamp() -> str:
    """Get current UTC timestamp in YYYY-MM-DD HH:MM:SS format"""
    return datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")

def generate_job_id() -> str:
    """Generate unique job ID"""
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"job_{timestamp}_{unique_id}"

def update_job_status(job_id: str, status: str, progress: int = 0, 
                     current_step: str = "", results: Dict = None, error: str = None):
    """Update job status in tracker"""
    if job_id in job_tracker:
        job_tracker[job_id].update({
            "status": status,
            "progress": progress,
            "current_step": current_step,
            "last_updated": get_current_utc_timestamp()
        })
        
        if results:
            job_tracker[job_id]["results"] = results
        if error:
            job_tracker[job_id]["error"] = error
        
        logger.info(f"📊 Job {job_id}: {status} - {current_step} ({progress}%)")

async def upload_to_s3(file_path: str, s3_key: str, bucket_name: str = None) -> str:
    """Upload file to S3 and return URL"""
    if not bucket_name:
        bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
    
    try:
        # Upload file
        s3_client.upload_file(file_path, bucket_name, s3_key)
        
        # Generate URL
        s3_url = f"s3://{bucket_name}/{s3_key}"
        logger.info(f"✅ Uploaded to S3: {s3_url}")
        return s3_url
        
    except ClientError as e:
        logger.error(f"❌ S3 upload failed: {e}")
        raise Exception(f"S3 upload failed: {e}")

def validate_plant_name(plant_name: str) -> str:
    """Validate and sanitize plant name"""
    if not plant_name or len(plant_name.strip()) < 2:
        raise ValueError("Plant name must be at least 2 characters")
    
    # Basic sanitization for filenames
    sanitized = plant_name.strip().replace('/', '_').replace('\\', '_')
    return sanitized

def validate_years(years: List[int]) -> List[int]:
    """Validate target years"""
    current_year = datetime.now().year
    valid_years = []
    
    for year in years:
        if 2000 <= year <= current_year:
            valid_years.append(year)
        else:
            logger.warning(f"⚠️ Invalid year ignored: {year}")
    
    if not valid_years:
        raise ValueError("No valid years provided (must be between 2000 and current year)")
    
    return sorted(valid_years)

# ===========================
# UPDATED S3 UPLOAD FUNCTIONS
# ===========================

async def upload_files_to_s3(plant_name: str, country_name: str, entity_id: str) -> Dict[str, str]:
    """
    Upload all result files to S3 - with smart directory detection
    """
    bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
    safe_plant_name = plant_name.replace(" ", "_").replace("/", "_")
    s3_prefix = f"{country_name}/{entity_id}"
    results = {}
    
    # Smart directory detection for final_output
    final_output_base = Path("final_output")
    final_output_dir = None
    
    logger.info(f"🔍 Looking for final_output directory for plant: '{plant_name}'")
    
    if final_output_base.exists():
        # List all directories in final_output for debugging
        existing_dirs = [d.name for d in final_output_base.iterdir() if d.is_dir()]
        logger.info(f"📁 Available final_output directories: {existing_dirs}")
        
        # Try multiple directory name patterns
        search_patterns = [
            plant_name,                                    # "Renew wind energy"
            plant_name.replace(" ", "_"),                  # "Renew_wind_energy"
            plant_name.replace(" ", "_").replace('"', ''), # Remove quotes
            safe_plant_name,                               # Sanitized version
        ]
        
        for pattern in search_patterns:
            test_dir = final_output_base / pattern
            logger.info(f"🔍 Trying pattern: '{pattern}' -> {test_dir}")
            
            if test_dir.exists():
                final_output_dir = test_dir
                logger.info(f"✅ Found final_output directory: {final_output_dir}")
                break
        
        # If exact matches fail, try fuzzy matching
        if not final_output_dir:
            # Normalize plant name for comparison
            normalized_plant = plant_name.lower().replace(" ", "").replace("_", "").replace("-", "")
            
            for existing_dir in final_output_base.iterdir():
                if existing_dir.is_dir():
                    normalized_existing = existing_dir.name.lower().replace(" ", "").replace("_", "").replace("-", "")
                    if normalized_plant == normalized_existing:
                        final_output_dir = existing_dir
                        logger.info(f"✅ Found final_output directory via fuzzy match: {final_output_dir}")
                        break
    
    if final_output_dir and final_output_dir.exists():
        logger.info("✅ Using final_output folder (STEP 5 completed)")
        return await upload_from_final_output(final_output_dir, s3_prefix, safe_plant_name, bucket_name)
    
    # FALLBACK: Use output_part2 folder if STEP 5 not found
    logger.warning("⚠️ final_output not found, falling back to output_part2 folder")
    
    # Find the most recent output_part2 directory
    output_part2_base = Path("output_part2")
    if not output_part2_base.exists():
        raise FileNotFoundError(f"Neither final_output nor output_part2 directories found")
    
    # Look for directories matching the plant name pattern
    plant_patterns = [
        plant_name.replace(" ", "_").replace('"', '').lower(),
        safe_plant_name.lower(),
        plant_name.lower().replace(" ", "_")
    ]
    
    matching_dirs = []
    for pattern in plant_patterns:
        matching_dirs.extend(list(output_part2_base.glob(f"{pattern}_complete_pipeline_*")))
    
    if not matching_dirs:
        # Try broader search
        matching_dirs = list(output_part2_base.glob(f"*_complete_pipeline_*"))
        logger.warning(f"⚠️ Using broader search, found: {[d.name for d in matching_dirs]}")
    
    if not matching_dirs:
        raise FileNotFoundError(f"No matching output directories found for plant: {plant_name}")
    
    # Use the most recent directory
    latest_dir = max(matching_dirs, key=lambda x: x.stat().st_mtime)
    logger.info(f"📁 Using fallback directory: {latest_dir}")
    
    return await upload_from_output_part2(latest_dir, s3_prefix, safe_plant_name, bucket_name, plant_name)

async def upload_from_final_output(final_output_dir: Path, s3_prefix: str, 
                                 safe_plant_name: str, bucket_name: str) -> Dict[str, str]:
    """Upload from final_output folder (preferred method) - Enhanced logging"""
    results = {}
    
    # Get all files for debugging
    all_files = list(final_output_dir.glob("*"))
    logger.info(f"📋 Files in final_output ({final_output_dir.name}): {[f.name for f in all_files]}")
    
    # Upload primary output - try multiple patterns
    primary_patterns = [
        "*financial_details.json",
        "*_financial_details.json",
        "*.json"  # Last resort
    ]
    
    primary_file = None
    for pattern in primary_patterns:
        primary_files = list(final_output_dir.glob(pattern))
        # Filter for financial details specifically
        primary_files = [f for f in primary_files if "financial_details" in f.name]
        if primary_files:
            primary_file = str(primary_files[0])
            break
    
    if primary_file:
        primary_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_details.json"
        results["primary_url"] = await upload_to_s3(primary_file, primary_s3_key, bucket_name)
        logger.info(f"✅ Uploaded primary: {primary_file}")
    else:
        # Try any JSON file as fallback
        json_files = list(final_output_dir.glob("*.json"))
        if json_files:
            primary_file = str(json_files[0])
            primary_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_details.json"
            results["primary_url"] = await upload_to_s3(primary_file, primary_s3_key, bucket_name)
            logger.info(f"✅ Uploaded primary (fallback): {primary_file}")
        else:
            raise FileNotFoundError(f"No JSON files found in final_output: {final_output_dir}")
    
    # Upload assumptions - try multiple patterns
    assumptions_patterns = [
        "*financial_assumptions.json",
        "*_financial_assumptions.json"
    ]
    
    assumptions_file = None
    for pattern in assumptions_patterns:
        assumptions_files = list(final_output_dir.glob(pattern))
        if assumptions_files:
            assumptions_file = str(assumptions_files[0])
            break
    
    if assumptions_file:
        assumptions_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_assumptions.json"
        results["assumptions_url"] = await upload_to_s3(assumptions_file, assumptions_s3_key, bucket_name)
        logger.info(f"✅ Uploaded assumptions: {assumptions_file}")
    else:
        results["assumptions_url"] = None
        logger.warning("⚠️ Assumptions file not found in final_output")
    
    # Upload static data
    static_file = final_output_dir / "static_data.json"
    if static_file.exists():
        static_s3_key = f"{s3_prefix}/static_data.json"
        results["static_url"] = await upload_to_s3(str(static_file), static_s3_key, bucket_name)
        logger.info(f"✅ Uploaded static data: {static_file}")
    else:
        # Create placeholder
        results["static_url"] = await create_and_upload_placeholder_static(s3_prefix, bucket_name)
    
    logger.info(f"🎉 Successfully uploaded from final_output: {final_output_dir.name}")
    return results

async def upload_from_output_part2(output_dir: Path, s3_prefix: str, 
                                  safe_plant_name: str, bucket_name: str, plant_name: str) -> Dict[str, str]:
    """Upload from output_part2 folder (fallback method)"""
    results = {}
    
    logger.info(f"📁 Searching in output_part2 directory: {output_dir}")
    
    # Look for the financial JSON file (from your pipeline)
    financial_files = list(output_dir.glob("*_financial.json"))
    if financial_files:
        financial_file = str(financial_files[0])
        primary_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_details.json"
        results["primary_url"] = await upload_to_s3(financial_file, primary_s3_key, bucket_name)
        logger.info(f"✅ Uploaded financial (fallback): {financial_file}")
    else:
        raise FileNotFoundError(f"No financial files found in {output_dir}")
    
    # Look for assumptions file
    assumptions_files = list(output_dir.glob("*_financial_assumptions.json"))
    if assumptions_files:
        assumptions_file = str(assumptions_files[0])
        assumptions_s3_key = f"{s3_prefix}/{safe_plant_name}_financial_assumptions.json"
        results["assumptions_url"] = await upload_to_s3(assumptions_file, assumptions_s3_key, bucket_name)
        logger.info(f"✅ Uploaded assumptions (fallback): {assumptions_file}")
    else:
        results["assumptions_url"] = None
        logger.warning("⚠️ Assumptions file not found in output_part2")
    
    # Create placeholder static data
    results["static_url"] = await create_and_upload_placeholder_static(s3_prefix, bucket_name, plant_name)
    
    return results

async def create_and_upload_placeholder_static(s3_prefix: str, bucket_name: str, 
                                             plant_name: str = None) -> str:
    """Create and upload placeholder static data"""
    placeholder_static = {
        "note": "Static data not available - generated by API", 
        "timestamp": get_current_utc_timestamp(),
        "plant_name": plant_name,
        "source": "API fallback"
    }
    
    temp_static_path = f"temp_static_{uuid.uuid4().hex[:8]}.json"
    with open(temp_static_path, 'w') as f:
        json.dump(placeholder_static, f, indent=2)
    
    static_s3_key = f"{s3_prefix}/static_data.json"
    static_url = await upload_to_s3(temp_static_path, static_s3_key, bucket_name)
    os.remove(temp_static_path)
    
    logger.info(f"✅ Created and uploaded placeholder static data")
    return static_url
async def upload_original_files_to_s3(uploaded_files: List[str], country_name: str, entity_id: str):
    """Upload original PDF files to S3"""
    bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
    s3_prefix = f"{country_name}/{entity_id}/uploaded_files"
    
    for file_path in uploaded_files:
        if Path(file_path).exists():
            filename = Path(file_path).name
            s3_key = f"{s3_prefix}/{filename}"
            try:
                await upload_to_s3(file_path, s3_key, bucket_name)
                logger.info(f"✅ Uploaded original file: {filename}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to upload {filename}: {e}")

# ===========================
# UPDATED BACKGROUND PROCESSING
# ===========================

async def process_pipeline_websearch(job_id: str, plant_name: str, country_name: str, 
                                   entity_id: str, target_years: List[int] = None):
    """Background task for web search processing - Updated for final_output folder"""
    try:
        update_job_status(job_id, "processing", 10, "Initializing pipeline...")
        
        # Initialize pipeline
        pipeline = CompleteIntegratedPipeline()
        update_job_status(job_id, "processing", 20, "Running web search and classification...")
        
        # Run complete pipeline with web search
        financial_path, assumptions_path, results = await pipeline.process_complete_pipeline(
            plant_name=plant_name,
            local_pdfs=None,  # Use web search
            target_years=target_years
        )
        
        if not financial_path:
            raise Exception("Pipeline processing failed - no financial data generated")
        
        update_job_status(job_id, "processing", 75, "Pipeline completed, STEP 5 generating final output...")
        
        # Wait for STEP 5 to complete and files to be written to final_output folder
        max_wait_time = 30  # Maximum 30 seconds
        wait_interval = 2   # Check every 2 seconds
        elapsed_time = 0
        
        final_output_dir = Path("final_output") / plant_name
        
        while elapsed_time < max_wait_time:
            if final_output_dir.exists():
                # Check if primary file exists
                primary_files = list(final_output_dir.glob("*financial_details.json"))
                if primary_files:
                    logger.info(f"✅ STEP 5 completed - final_output folder ready")
                    break
            
            await asyncio.sleep(wait_interval)
            elapsed_time += wait_interval
            update_job_status(job_id, "processing", 75 + (elapsed_time * 5 // max_wait_time), 
                            f"Waiting for STEP 5 completion... ({elapsed_time}s)")
        
        if elapsed_time >= max_wait_time:
            logger.warning(f"⚠️ STEP 5 timeout - proceeding with available files")
        
        update_job_status(job_id, "processing", 85, "Uploading final output to S3...")
        
        # Upload files from final_output folder to S3
        s3_results = await upload_files_to_s3(
            plant_name=plant_name,
            country_name=country_name,
            entity_id=entity_id
        )
        
        update_job_status(job_id, "processing", 95, "Finalizing results...")
        
        # Prepare final results
        successful_years = results.get("merged_results", {}).get("successful_years", [])
        total_years = len(target_years) if target_years else len(successful_years)
        
        final_results = {
            "primary_output": {
                "url": s3_results["primary_url"],
                "type": "financial_details",
                "description": "Enhanced multi-year financial data with debt/equity analysis",
                "source": f"final_output/{plant_name}/"
            },
            "static_data": {
                "url": s3_results["static_url"],
                "type": "static_reference", 
                "description": "Static reference data",
                "source": f"final_output/{plant_name}/"
            },
            "processed_years": successful_years,
            "success_rate": f"{len(successful_years)}/{total_years} years",
            "processing_summary": {
                "total_pdfs_processed": total_years,
                "successful_extractions": len(successful_years),
                "pipeline_mode": "websearch",
                "final_output_location": f"final_output/{plant_name}/",
                "step_5_completion_time": elapsed_time,
                "processing_time": get_current_utc_timestamp()
            }
        }
        
        # Add assumptions if available
        if s3_results["assumptions_url"]:
            final_results["assumptions_output"] = {
                "url": s3_results["assumptions_url"],
                "type": "financial_assumptions",
                "description": "Latest year financial assumptions and WACC",
                "source": f"final_output/{plant_name}/"
            }
        
        update_job_status(job_id, "completed", 100, "Processing completed successfully", final_results)
        
    except Exception as e:
        error_msg = f"Pipeline processing failed: {str(e)}"
        logger.error(f"❌ Job {job_id}: {error_msg}")
        logger.error(traceback.format_exc())
        update_job_status(job_id, "failed", 0, "Processing failed", error=error_msg)

async def process_pipeline_upload(job_id: str, plant_name: str, country_name: str, 
                                entity_id: str, uploaded_files: List[str], target_years: List[int] = None):
    """Background task for file upload processing - Updated for final_output folder"""
    try:
        update_job_status(job_id, "processing", 10, "Processing uploaded files...")
        
        # Initialize pipeline
        pipeline = CompleteIntegratedPipeline()
        update_job_status(job_id, "processing", 20, "Running classification on uploaded files...")
        
        # Run complete pipeline with uploaded files
        financial_path, assumptions_path, results = await pipeline.process_complete_pipeline(
            plant_name=plant_name,
            local_pdfs=uploaded_files,  # Use uploaded files
            target_years=target_years
        )
        
        if not financial_path:
            raise Exception("Pipeline processing failed - no financial data generated")
        
        update_job_status(job_id, "processing", 70, "Pipeline completed, STEP 5 generating final output...")
        
        # Wait for STEP 5 to complete and files to be written to final_output folder
        max_wait_time = 30  # Maximum 30 seconds
        wait_interval = 2   # Check every 2 seconds
        elapsed_time = 0
        
        final_output_dir = Path("final_output") / plant_name
        
        while elapsed_time < max_wait_time:
            if final_output_dir.exists():
                # Check if primary file exists
                primary_files = list(final_output_dir.glob("*financial_details.json"))
                if primary_files:
                    logger.info(f"✅ STEP 5 completed - final_output folder ready")
                    break
            
            await asyncio.sleep(wait_interval)
            elapsed_time += wait_interval
            update_job_status(job_id, "processing", 70 + (elapsed_time * 10 // max_wait_time), 
                            f"Waiting for STEP 5 completion... ({elapsed_time}s)")
        
        if elapsed_time >= max_wait_time:
            logger.warning(f"⚠️ STEP 5 timeout - proceeding with available files")
        
        update_job_status(job_id, "processing", 85, "Uploading original PDFs and final output to S3...")
        
        # Upload original PDFs to S3 first
        await upload_original_files_to_s3(uploaded_files, country_name, entity_id)
        
        # Upload result files from final_output folder to S3
        s3_results = await upload_files_to_s3(
            plant_name=plant_name,
            country_name=country_name,
            entity_id=entity_id
        )
        
        update_job_status(job_id, "processing", 95, "Finalizing results...")
        
        # Prepare final results
        successful_years = results.get("merged_results", {}).get("successful_years", [])
        total_years = len(uploaded_files)
        
        final_results = {
            "primary_output": {
                "url": s3_results["primary_url"],
                "type": "financial_details",
                "description": "Enhanced multi-year financial data with debt/equity analysis",
                "source": f"final_output/{plant_name}/"
            },
            "static_data": {
                "url": s3_results["static_url"],
                "type": "static_reference",
                "description": "Static reference data",
                "source": f"final_output/{plant_name}/"
            },
            "processed_years": successful_years,
            "success_rate": f"{len(successful_years)}/{total_years} files",
            "processing_summary": {
                "total_pdfs_processed": total_years,
                "successful_extractions": len(successful_years),
                "pipeline_mode": "upload",
                "uploaded_files_count": len(uploaded_files),
                "final_output_location": f"final_output/{plant_name}/",
                "step_5_completion_time": elapsed_time,
                "processing_time": get_current_utc_timestamp()
            }
        }
        
        # Add assumptions if available
        if s3_results["assumptions_url"]:
            final_results["assumptions_output"] = {
                "url": s3_results["assumptions_url"],
                "type": "financial_assumptions",
                "description": "Latest year financial assumptions and WACC",
                "source": f"final_output/{plant_name}/"
            }
        
        update_job_status(job_id, "completed", 100, "Processing completed successfully", final_results)
        
        # Cleanup temporary files
        for temp_file in uploaded_files:
            try:
                os.remove(temp_file)
                logger.info(f"🗑️ Cleaned up temp file: {temp_file}")
            except:
                pass
        
    except Exception as e:
        error_msg = f"Pipeline processing failed: {str(e)}"
        logger.error(f"❌ Job {job_id}: {error_msg}")
        logger.error(traceback.format_exc())
        update_job_status(job_id, "failed", 0, "Processing failed", error=error_msg)
        
        # Cleanup temporary files on error
        for temp_file in uploaded_files:
            try:
                os.remove(temp_file)
            except:
                pass

# ===========================
# API ENDPOINTS
# ===========================

@app.get("/", summary="Health Check")
async def root():
    """Health check endpoint"""
    return {
        "service": "Financial Pipeline API",
        "status": "healthy",
        "user": "Gangatharangurusamy",
        "timestamp": get_current_utc_timestamp(),
        "version": "2.0.0",
        "source": "final_output folder (STEP 5)",
        "updated": "2025-07-07 11:06:00 UTC"
    }

@app.post("/process/websearch", summary="Process with Web Search")
async def process_websearch(
    request: ProcessWebSearchRequest,
    background_tasks: BackgroundTasks
):
    """
    Process financial data using web search for PDF acquisition.
    Downloads PDFs automatically for the specified plant and years.
    Uploads results from final_output folder to S3.
    """
    try:
        # Validate inputs
        plant_name = validate_plant_name(request.plant_name)
        target_years = validate_years(request.target_years) if request.target_years else None
        
        # Generate job ID
        job_id = generate_job_id()
        
        # Initialize job tracking
        job_tracker[job_id] = {
            "job_id": job_id,
            "status": "processing",
            "user": "Gangatharangurusamy",
            "timestamp": get_current_utc_timestamp(),
            "progress": 0,
            "current_step": "Initializing...",
            "country_name": request.country_name,
            "entity_id": request.entity_id,
            "plant_name": plant_name,
            "mode": "websearch",
            "target_years": target_years,
            "results": None,
            "error": None,
            "source": "final_output folder"
        }
        
        # Start background processing
        background_tasks.add_task(
            process_pipeline_websearch,
            job_id, plant_name, request.country_name, request.entity_id, target_years
        )
        
        logger.info(f"🚀 Started websearch job {job_id} for {plant_name}")
        
        return JSONResponse({
            "job_id": job_id,
            "status": "processing",
            "user": "Gangatharangurusamy",
            "timestamp": get_current_utc_timestamp(),
            "message": f"Pipeline started for {plant_name}",
            "estimated_time": "15-30 minutes",
            "mode": "websearch",
            "country_name": request.country_name,
            "entity_id": request.entity_id,
            "target_years": target_years,
            "source": "final_output folder (STEP 5)"
        })
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Websearch request failed: {e}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/process/upload", summary="Process with File Upload")
async def process_upload(
    background_tasks: BackgroundTasks,
    plant_name: str = Form(...),
    country_name: str = Form(...),
    entity_id: str = Form(...),
    target_years: str = Form(None),  # JSON string of years
    files: List[UploadFile] = File(...)
):
    """
    Process financial data using uploaded PDF files.
    Upload multiple PDF files for processing.
    Uploads results from final_output folder to S3.
    """
    try:
        # Validate inputs
        plant_name = validate_plant_name(plant_name)
        
        # Parse target years
        target_years_list = None
        if target_years:
            try:
                target_years_list = json.loads(target_years)
                target_years_list = validate_years(target_years_list)
            except (json.JSONDecodeError, ValueError) as e:
                raise ValueError(f"Invalid target_years format: {e}")
        
        # Validate files
        if not files or len(files) == 0:
            raise ValueError("No files uploaded")
        
        # Check file types
        pdf_files = []
        for file in files:
            if not file.filename.lower().endswith('.pdf'):
                raise ValueError(f"Invalid file type: {file.filename}. Only PDF files are allowed.")
            pdf_files.append(file)
        
        if len(pdf_files) > 10:  # Reasonable limit
            raise ValueError("Too many files. Maximum 10 PDF files allowed.")
        
        # Generate job ID
        job_id = generate_job_id()
        
        # Save uploaded files temporarily
        temp_dir = Path(tempfile.mkdtemp(prefix=f"upload_{job_id}_"))
        saved_files = []
        
        for file in pdf_files:
            temp_file_path = temp_dir / file.filename
            with open(temp_file_path, "wb") as temp_file:
                content = await file.read()
                temp_file.write(content)
            saved_files.append(str(temp_file_path))
            logger.info(f"📁 Saved uploaded file: {file.filename}")
        
        # Initialize job tracking
        job_tracker[job_id] = {
            "job_id": job_id,
            "status": "processing",
            "user": "Gangatharangurusamy",
            "timestamp": get_current_utc_timestamp(),
            "progress": 0,
            "current_step": "Processing uploaded files...",
            "country_name": country_name,
            "entity_id": entity_id,
            "plant_name": plant_name,
            "mode": "upload",
            "target_years": target_years_list,
            "uploaded_files": [f.filename for f in pdf_files],
            "results": None,
            "error": None,
            "source": "final_output folder"
        }
        
        # Start background processing
        background_tasks.add_task(
            process_pipeline_upload,
            job_id, plant_name, country_name, entity_id, saved_files, target_years_list
        )
        
        logger.info(f"🚀 Started upload job {job_id} for {plant_name} with {len(pdf_files)} files")
        
        return JSONResponse({
            "job_id": job_id,
            "status": "processing",
            "user": "Gangatharangurusamy",
            "timestamp": get_current_utc_timestamp(),
            "message": f"Pipeline started for {plant_name} with {len(pdf_files)} uploaded files",
            "estimated_time": "10-25 minutes",
            "mode": "upload",
            "country_name": country_name,
            "entity_id": entity_id,
            "uploaded_files": [f.filename for f in pdf_files],
            "target_years": target_years_list,
            "source": "final_output folder (STEP 5)"
        })
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Upload request failed: {e}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.get("/status/{job_id}", summary="Get Job Status")
async def get_job_status(job_id: str):
    """
    Get the current status of a processing job.
    Returns progress, current step, and results when completed.
    """
    if job_id not in job_tracker:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")
    
    job_data = job_tracker[job_id]
    
    return JSONResponse({
        "job_id": job_data["job_id"],
        "status": job_data["status"],
        "user": job_data["user"],
        "timestamp": job_data["timestamp"],
        "last_updated": job_data.get("last_updated", job_data["timestamp"]),
        "progress": job_data["progress"],
        "current_step": job_data["current_step"],
        "country_name": job_data["country_name"],
        "entity_id": job_data["entity_id"],
        "plant_name": job_data["plant_name"],
        "mode": job_data["mode"],
        "target_years": job_data.get("target_years"),
        "source": job_data.get("source", "final_output folder"),
        "results": job_data.get("results"),
        "error": job_data.get("error")
    })

@app.get("/results/{country_name}/{entity_id}", summary="Get Processing Results")
async def get_results(
    country_name: str, 
    entity_id: str,
    file_type: str = Query("primary", description="File type to retrieve: 'primary', 'assumptions', or 'static'")
):
    """
    Retrieve processing results from S3 for a specific country and entity.
    Returns the JSON content of the requested file type.
    """
    try:
        bucket_name = os.getenv('S3_BUCKET_NAME', 'aws-demo-22')
        
        # Map file types to potential S3 keys
        file_mappings = {
            "primary": "_financial_details.json",
            "assumptions": "_financial_assumptions.json", 
            "static": "static_data.json"
        }
        
        if file_type not in file_mappings:
            raise HTTPException(status_code=400, detail=f"Invalid file_type. Must be one of: {list(file_mappings.keys())}")
        
        # List objects in the S3 prefix to find the right file
        s3_prefix = f"{country_name}/{entity_id}/"
        
        try:
            response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=s3_prefix)
            
            if 'Contents' not in response:
                raise HTTPException(status_code=404, detail=f"No files found for {country_name}/{entity_id}")
            
            # Find the matching file
            target_suffix = file_mappings[file_type]
            matching_key = None
            
            for obj in response['Contents']:
                key = obj['Key']
                if key.endswith(target_suffix):
                    matching_key = key
                    break
            
            if not matching_key:
                raise HTTPException(status_code=404, detail=f"File type '{file_type}' not found for {country_name}/{entity_id}")
            
            # Download and return file content
            file_obj = s3_client.get_object(Bucket=bucket_name, Key=matching_key)
            file_content = file_obj['Body'].read().decode('utf-8')
            
            return JSONResponse({
                "country_name": country_name,
                "entity_id": entity_id,
                "file_type": file_type,
                "s3_key": matching_key,
                "s3_url": f"s3://{bucket_name}/{matching_key}",
                "retrieved_at": get_current_utc_timestamp(),
                "source": "final_output folder",
                "data": json.loads(file_content)
            })
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                raise HTTPException(status_code=404, detail=f"File not found in S3: {country_name}/{entity_id}")
            else:
                raise HTTPException(status_code=500, detail=f"S3 error: {e}")
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="Invalid JSON content in S3 file")
    except Exception as e:
        logger.error(f"❌ Results retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve results: {str(e)}")

@app.get("/jobs", summary="List All Jobs")
async def list_jobs(
    status: str = Query(None, description="Filter by status: 'processing', 'completed', 'failed'"),
    country_name: str = Query(None, description="Filter by country"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of jobs to return")
):
    """
    List all processing jobs with optional filtering.
    """
    jobs = list(job_tracker.values())
    
    # Apply filters
    if status:
        jobs = [job for job in jobs if job.get("status") == status]
    
    if country_name:
        jobs = [job for job in jobs if job.get("country_name") == country_name]
    
    # Sort by timestamp (newest first) and limit
    jobs = sorted(jobs, key=lambda x: x.get("timestamp", ""), reverse=True)[:limit]
    
    return JSONResponse({
        "total_jobs": len(jobs),
        "filtered_jobs": len(jobs),
        "user": "Gangatharangurusamy",
        "timestamp": get_current_utc_timestamp(),
        "source": "final_output folder",
        "jobs": jobs
    })

@app.delete("/jobs/{job_id}", summary="Delete Job")
async def delete_job(job_id: str):
    """
    Delete a job from the tracker.
    """
    if job_id not in job_tracker:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")
    
    deleted_job = job_tracker.pop(job_id)
    
    return JSONResponse({
        "message": f"Job {job_id} deleted successfully",
        "deleted_job": deleted_job,
        "user": "Gangatharangurusamy",
        "timestamp": get_current_utc_timestamp()
    })

@app.get("/final-output/{plant_name}", summary="Check Final Output Status")
async def check_final_output(plant_name: str):
    """
    Check the status of final_output folder for a specific plant.
    Useful for debugging STEP 5 completion.
    """
    try:
        final_output_dir = Path("final_output") / plant_name
        
        if not final_output_dir.exists():
            return JSONResponse({
                "plant_name": plant_name,
                "status": "not_found",
                "message": f"final_output/{plant_name} directory does not exist",
                "timestamp": get_current_utc_timestamp()
            })
        
        # List all files in the directory
        all_files = list(final_output_dir.glob("*"))
        file_details = []
        
        for file_path in all_files:
            if file_path.is_file():
                file_details.append({
                    "name": file_path.name,
                    "size": file_path.stat().st_size,
                    "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                })
        
        # Check for expected files
        expected_files = {
            "financial_details": any("financial_details.json" in f["name"] for f in file_details),
            "financial_assumptions": any("financial_assumptions.json" in f["name"] for f in file_details),
            "static_data": any("static_data.json" in f["name"] for f in file_details)
        }
        
        return JSONResponse({
            "plant_name": plant_name,
            "status": "exists",
            "directory": str(final_output_dir),
            "file_count": len(file_details),
            "files": file_details,
            "expected_files": expected_files,
            "ready_for_s3": expected_files["financial_details"],
            "timestamp": get_current_utc_timestamp()
        })
        
    except Exception as e:
        logger.error(f"❌ Final output check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to check final output: {str(e)}")

# ===========================
# ERROR HANDLERS
# ===========================

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"❌ Unhandled exception: {exc}")
    logger.error(traceback.format_exc())
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc),
            "timestamp": get_current_utc_timestamp(),
            "user": "Gangatharangurusamy"
        }
    )

# ===========================
# MAIN APPLICATION
# ===========================

if __name__ == "__main__":
    print("🚀 Financial Pipeline API")
    print(f"📅 Date: 2025-07-07 11:06:00 UTC")
    print(f"👤 User: Gangatharangurusamy")
    print(f"📁 Source: final_output folder (STEP 5)")
    print("="*50)
    print("🔧 Required Environment Variables:")
    print("   • GEMINI_API_KEY")
    print("   • OPENAI_API_KEY") 
    print("   • AWS_ACCESS_KEY_ID")
    print("   • AWS_SECRET_ACCESS_KEY")
    print("   • AWS_REGION (optional, defaults to us-east-1)")
    print("   • S3_BUCKET_NAME (optional, defaults to aws-demo-22)")
    print("="*50)
    print("📚 API Endpoints:")
    print("   • POST /process/websearch - Process with web search")
    print("   • POST /process/upload - Process with file upload")
    print("   • GET /status/{job_id} - Get job status")
    print("   • GET /results/{country_name}/{entity_id} - Get results")
    print("   • GET /final-output/{plant_name} - Check final output status")
    print("   • GET /jobs - List all jobs")
    print("   • GET /docs - API documentation")
    print("="*50)
    print("📁 File Flow:")
    print("   Pipeline → final_output/{plant_name}/ → S3 upload")
    print("="*50)
    
    uvicorn.run(
        "financial_pipeline_api_updated:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )