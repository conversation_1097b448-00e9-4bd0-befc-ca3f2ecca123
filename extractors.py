"""
Focused Financial Data Extractor - Separate Balance Sheet & Profit Loss
Author: Gangatharangurusamy
Description: Extract financial data with focused prompts per statement type
Model: GPT-4o for precise extraction
Strategy: Separate extraction, then combine results
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
import base64
import os

# Required imports
try:
    from openai import OpenAI
    from PIL import Image
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Install with: pip install openai pillow")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FocusedFinancialExtractor:
    """
    Focused extractor with separate prompts for Balance Sheet and Profit & Loss
    """
    
    # Balance Sheet focused prompt
    BALANCE_SHEET_PROMPT = """
    You are extracting ONLY Balance Sheet data from financial statement images.

    🚨 CRITICAL MULTIPLIER RULES:
    - If document shows "in millions" or "₹ million" or "Rm millions" → multiply by 1,000,000 (10^6)
    - If document shows "in billions" or "₹ billion" or "Rm billions" → multiply by 1,000,000,000 (10^9)  
    - If document shows "in thousands" or "₹ thousand" or "Rm thousands" → multiply by 1,000 (10^3)
    - If document shows "in crores" → multiply by 10,000,000 (10^7)
    - If document shows "in lakhs" → multiply by 100,000 (10^5)
    - If no multiplier mentioned, use the raw number as-is

    🔍 CURRENCY & COUNTRY DETECTION:
    - "Rs", "₹", "INR" → Currency: "INR", Country: "India"
    - "R", "Rm", "ZAR" → Currency: "ZAR", Country: "South Africa"  
    - "$", "USD" → Currency: "USD", Country: "United States"
    - "€", "EUR" → Currency: "EUR", Country: "Germany"

    📊 EXTRACT BALANCE SHEET DATA ONLY:

    Look for these exact items in the Balance Sheet:
    - Company/Plant name from the header
    - Property, plant and equipment (PPE) / Fixed Assets / Non-current assets
    - Current assets
    - Cash and cash equivalents  
    - Current liabilities
    - Short-term borrowings / Short-term debt
    - Long-term borrowings / Long-term debt / Non-current liabilities
    - Total equity / Shareholders' equity
    - Note references (e.g., "Note 8" for PPE)

    🎯 REQUIRED JSON OUTPUT:
    ```json
    {
      "extraction_metadata": {
        "statement_type": "balance_sheet",
        "extraction_timestamp": "2025-07-02T11:19:56Z",
        "model_used": "gpt-4o",
        "multiplier_applied": true
      },
      "balance_sheet": {
        "country": "",
        "currency": "",
        "multiplier_detected": "",
        "plant_name": "",
        "Property, plant and equipment": ",
        "current_assets": "",
        "cash_and_cash_equivalents": "",
        "current_liabilities": "",
        "short_term_borrowings": "",
        "Long_Term_borrowing": "",
        "total_equity": "",
        "ppe_note_reference": ""
      }
    }
    ```

    🚨 INSTRUCTIONS:
    1. ONLY extract Balance Sheet data - ignore P&L items
    2. Apply multipliers to get final numerical values
    3. Detect currency from symbols and text
    4. Extract note references exactly as shown
    5. Return ONLY the JSON, no explanations
    6. All numbers should be AFTER multiplier application
    7. If data is not found, set the value to 0 for the following numerical fields - Property, plant and equipment, current_assets, cash_and_cash_equivalents, current_liabilities, short_term_borrowings, Long_Term_borrowing, total_equity  and should not put null or not_found for these fields.
    8. if the data is not found, then put not_found for the following fields - country, currency, multiplier_detected, plant_name and ppe_note_reference.
    9.Please consider only stand alone balance sheet and not consolidated balance sheet.ignore the consolidated balance sheet.

    """

    # Profit & Loss focused prompt  
    PROFIT_LOSS_PROMPT = """
    You are extracting ONLY Profit & Loss data from financial statement images.

    🚨 CRITICAL MULTIPLIER RULES:
    - If document shows "in millions" or "₹ million" or "Rm millions" → multiply by 1,000,000 (10^6)
    - If document shows "in billions" or "₹ billion" or "Rm billions" → multiply by 1,000,000,000 (10^9)  
    - If document shows "in thousands" or "₹ thousand" or "Rm thousands" → multiply by 1,000 (10^3)
    - If document shows "in crores" → multiply by 10,000,000 (10^7)
    - If document shows "in lakhs" → multiply by 100,000 (10^5)
    - If no multiplier mentioned, use the raw number as-is

    📈 EXTRACT PROFIT & LOSS DATA ONLY:

    Look for these exact items in the Profit & Loss Statement:
    - Revenue / Turnover / Sales
    - Total income / Total revenue
    - Finance costs / Interest expense / Financial expenses
    - Profit before tax / Profit before interest and tax
    - Net profit / Net income / Profit after tax
    - Note references for finance costs and tax

    🎯 REQUIRED JSON OUTPUT:
    ```json
    {
      "extraction_metadata": {
        "statement_type": "profit_loss", 
        "extraction_timestamp": "2025-07-02T11:19:56Z",
        "model_used": "gpt-4o",
        "multiplier_applied": true
      },
      "profit_loss": {
        "multiplier_detected": "",
        "revenue": "",
        "finance_cost": ,
        "profit_before_tax": "" ,
        "net_income": "",
        "financial_cost_note": [""],
        "tax_note_references": [""]
      }
    }
    ```

    🚨 INSTRUCTIONS:
    1. ONLY extract Profit & Loss data - ignore Balance Sheet items
    2. Apply multipliers to get final numerical values
    3. BRACKET NOTATION HANDLING: Values in parentheses "()" indicate negative amounts in financial statements, but for EXPENSES ONLY we need to REVERSE this logic:
       - For EXPENSES (finance_cost, exceptional_items, etc.): If source shows (184) → extract as +184 (positive value), If source shows 184 → extract as -184 (negative value)
       - For REVENUE: Keep normal logic - If source shows (123) → extract as -123 (negative value), If source shows 123 → extract as +123 (positive value)
    4. REVENUE SOURCE PRIORITY: Always prioritize "adjusted performance" or "underlying performance" sections when available. Only use standard format if adjusted/underlying performance is not provided.
    5. EXCEPTIONAL ITEMS: Extract "exceptional_items" from specific exceptional items line in the financial statements, NOT from "profit before tax".
    6. Extract note references exactly as shown
    7. Return ONLY the JSON, no explanations
    8. All numbers should be AFTER multiplier application
    9. If data is not found, set the value to 0 for the following numerical fields - revenue, finance_cost, exceptional_items, profit_before_tax, net_income and should not put null or not_found for these fields.
    10. if the data is not found, then put not_found for the following fields - multiplier_detected, financial_cost_note and tax_note_references.
    11. Please consider only stand alone profit and loss statement and not consolidated profit and loss statement.ignore the consolidated profit and loss statement.



    """

    def __init__(self):
        """Initialize the focused extractor with GPT-4o"""
        # Check for OpenAI API key
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY not found. Please set it in .env file")
        
        # Initialize OpenAI client
        self.client = OpenAI(api_key=api_key)
        
        logger.info("✅ Focused Financial Extractor initialized with GPT-4o")
        logger.info("🎯 Strategy: Separate extraction for Balance Sheet and Profit & Loss")

    def encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64 for OpenAI API"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def extract_balance_sheet(self, balance_sheet_images: List[str]) -> Dict[str, Any]:
        """
        Extract ONLY Balance Sheet data using focused prompt
        """
        logger.info("📊 Extracting Balance Sheet data with focused prompt...")
        
        try:
            # Prepare content for Balance Sheet extraction
            content = [
                {
                    "type": "text",
                    "text": self.BALANCE_SHEET_PROMPT
                }
            ]
            
            # Add balance sheet images
            for i, image_path in enumerate(balance_sheet_images[:3]):  # Limit to 3 images
                image_base64 = self.encode_image_base64(image_path)
                
                content.append({
                    "type": "text",
                    "text": f"\n📊 Balance Sheet Image {i+1}:"
                })
                
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_base64}"
                    }
                })
            
            # Call GPT-4o for Balance Sheet extraction
            logger.info("🤖 Calling GPT-4o for Balance Sheet extraction...")
            
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                max_tokens=2000,
                temperature=0
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            # Save raw GPT response to file for debugging
            # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # raw_filename = f"gpt_raw_balance_sheet_{timestamp}.txt"
            # with open(raw_filename, "w", encoding="utf-8") as f:
            #     f.write(response_text)
            # logger.info(f"📝 Saved raw GPT Balance Sheet response to: {raw_filename}")

            # Parse JSON
            balance_sheet_data = json.loads(response_text)
            
            logger.info("✅ Balance Sheet extraction completed successfully")
            # self._log_balance_sheet_summary(balance_sheet_data)
            
            return balance_sheet_data
            
        except Exception as e:
            logger.error(f"❌ Balance Sheet extraction failed: {e}")
            raise Exception(f"Balance Sheet extraction failed: {e}")

    def extract_profit_loss(self, profit_loss_images: List[str]) -> Dict[str, Any]:
        """
        Extract ONLY Profit & Loss data using focused prompt
        """
        logger.info("📈 Extracting Profit & Loss data with focused prompt...")
        
        try:
            # Prepare content for Profit & Loss extraction
            content = [
                {
                    "type": "text",
                    "text": self.PROFIT_LOSS_PROMPT
                }
            ]
            
            # Add profit & loss images
            for i, image_path in enumerate(profit_loss_images[:3]):  # Limit to 3 images
                image_base64 = self.encode_image_base64(image_path)
                
                content.append({
                    "type": "text",
                    "text": f"\n📈 Profit & Loss Image {i+1}:"
                })
                
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_base64}"
                    }
                })
            
            # Call GPT-4o for Profit & Loss extraction
            logger.info("🤖 Calling GPT-4o for Profit & Loss extraction...")
            
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                max_tokens=2000,
                temperature=0
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # raw_filename = f"gpt_raw_profit_loss_{timestamp}.txt"
            # with open(raw_filename, "w", encoding="utf-8") as f:
            #     f.write(response_text)
            # logger.info(f"📝 Saved raw GPT Profit & Loss response to: {raw_filename}")

                # Save raw GPT response to file for debugging         
            # Parse JSON
            profit_loss_data = json.loads(response_text)
            
            logger.info("✅ Profit & Loss extraction completed successfully")
            self._log_profit_loss_summary(profit_loss_data)
            
            return profit_loss_data
            
        except Exception as e:
            logger.error(f"❌ Profit & Loss extraction failed: {e}")
            raise Exception(f"Profit & Loss extraction failed: {e}")

    def extract_financial_data(self, classified_images: Dict[str, List[str]]) -> Dict[str, Any]:
        """
        Main extraction method - combines focused extractions (FIXED)
        """
        logger.info("🚀 Starting focused financial data extraction...")
        
        # FIXED: Initialize proper structure from the start
        extracted_data = {
            "extraction_metadata": {
                "extraction_timestamp": datetime.now().isoformat(),
                "extraction_strategy": "focused_separate_prompts",
                "model_used": "gpt-4o",
                "statements_processed": []
            }
        }
        
        # Extract Balance Sheet data
        if "Statement of Balance Sheet" in classified_images:
            logger.info("📊 Processing Balance Sheet images...")
            try:
                balance_sheet_result = self.extract_balance_sheet(
                    classified_images["Statement of Balance Sheet"]
                )
                
                # Extract only the balance_sheet data, not metadata
                if "balance_sheet" in balance_sheet_result:
                    extracted_data["balance_sheet"] = balance_sheet_result["balance_sheet"]
                    extracted_data["extraction_metadata"]["statements_processed"].append("balance_sheet")
                    logger.info("✅ Balance Sheet data merged successfully")
                else:
                    logger.warning("⚠️ No balance sheet data found in extraction result")
                    
            except Exception as e:
                logger.error(f"❌ Balance Sheet extraction failed: {e}")
                # Continue with other extractions
        
        # Extract Profit & Loss data
        if "Statement of Profit and Loss" in classified_images:
            logger.info("📈 Processing Profit & Loss images...")
            try:
                profit_loss_result = self.extract_profit_loss(
                    classified_images["Statement of Profit and Loss"]
                )
                
                # Extract only the profit_loss data, not metadata
                if "profit_loss" in profit_loss_result:
                    extracted_data["profit_loss"] = profit_loss_result["profit_loss"]
                    extracted_data["extraction_metadata"]["statements_processed"].append("profit_loss")
                    logger.info("✅ Profit & Loss data merged successfully")
                else:
                    logger.warning("⚠️ No profit & loss data found in extraction result")
                    
            except Exception as e:
                logger.error(f"❌ Profit & Loss extraction failed: {e}")
                # Continue with pipeline
        
        # Check if we got any data
        if not extracted_data["extraction_metadata"]["statements_processed"]:
            raise Exception("No financial statements were successfully extracted")
        
        # Validate consistency between statements (only if we have both)
        if len(extracted_data["extraction_metadata"]["statements_processed"]) > 1:
            try:
                self._validate_consistency(extracted_data)
            except Exception as e:
                logger.warning(f"⚠️ Consistency validation failed: {e}")
        
        logger.info("🎉 Focused financial data extraction completed successfully")
        logger.info(f"📊 Statements processed: {extracted_data['extraction_metadata']['statements_processed']}")
        
        return extracted_data

    def _log_balance_sheet_summary(self, balance_sheet_data: Dict[str, Any]) -> None:
        """Log Balance Sheet extraction summary"""
        if "balance_sheet" in balance_sheet_data:
            bs = balance_sheet_data["balance_sheet"]
            logger.info("📊 BALANCE SHEET SUMMARY:")
            logger.info(f"  🏭 Company: {bs.get('plant_name', 'Unknown')}")
            logger.info(f"  🌍 Country: {bs.get('country', 'Unknown')}")
            logger.info(f"  💱 Currency: {bs.get('currency', 'Unknown')}")
            logger.info(f"  🔢 Multiplier: {bs.get('multiplier_detected', 'Unknown')}")
            
            for field, label in [
                ("Property, plant and equipment", "🏗️ PPE"),
                ("total_equity", "💰 Total Equity"),
                ("short_term_borrowings", "💸 Short-term Borrowings"),
                ("Long_Term_borrowing", "🏦 Long-term Borrowings"),
            ]:
                val = bs.get(field)
                if isinstance(val, (int, float)):
                    logger.info(f"  {label}: {val:,.0f}")
                elif val == "not_found":
                    logger.warning(f"  {label} explicitly marked as not found")
                else:
                    logger.warning(f"  {label} missing or invalid (got {val})")


    def _log_profit_loss_summary(self, profit_loss_data: Dict[str, Any]) -> None:
        """Log Profit & Loss extraction summary"""
        if "profit_loss" in profit_loss_data:
            pl = profit_loss_data["profit_loss"]
            logger.info("📈 PROFIT & LOSS SUMMARY:")
            logger.info(f"  🔢 Multiplier: {pl.get('multiplier_detected', 'Unknown')}")

            for field, label in [
                ("revenue", "💰 Revenue"),
                ("finance_cost", "💸 Finance Cost"),
                ("profit_before_tax", "📊 Profit Before Tax"),
                ("net_income", "📈 Net Income"),
            ]:
                val = pl.get(field)
                if isinstance(val, (int, float)):
                    logger.info(f"  {label}: {val:,.0f}")
                elif val == "not_found":
                    logger.warning(f"  {label} explicitly marked as not found")
                else:
                    logger.warning(f"  {label} missing or invalid (got {val})")

    def _validate_consistency(self, extracted_data: Dict[str, Any]) -> None:
        """Validate consistency between Balance Sheet and Profit & Loss"""
        logger.info("🔍 Validating consistency between statements...")

        # Check if both statements use same multiplier
        bs_multiplier = extracted_data.get("balance_sheet", {}).get("multiplier_detected")
        pl_multiplier = extracted_data.get("profit_loss", {}).get("multiplier_detected")

        if bs_multiplier and pl_multiplier and bs_multiplier != pl_multiplier:
            logger.warning(f"⚠️ Multiplier mismatch: Balance Sheet uses {bs_multiplier}, P&L uses {pl_multiplier}")

        # Check for reasonable values - FIXED: Handle string values safely
        equity = extracted_data.get("balance_sheet", {}).get("total_equity", 0)
        net_income = extracted_data.get("profit_loss", {}).get("net_income", 0)

        # Convert to numeric values safely
        try:
            equity_num = self._safe_numeric_conversion(equity)
            net_income_num = self._safe_numeric_conversion(net_income)

            if equity_num > 0 and net_income_num != 0:
                roe = net_income_num / equity_num
                if abs(roe) > 1.0:  # ROE > 100%
                    logger.warning(f"⚠️ Unusual ROE detected: {roe:.1%}")
        except Exception as e:
            logger.warning(f"⚠️ Could not validate ROE: {e}")

        logger.info("✅ Consistency validation completed")

    def _safe_numeric_conversion(self, value) -> float:
        """Safely convert value to numeric, handling strings and other types"""
        if value is None:
            return 0.0

        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            # Handle special cases
            if value.lower() in ['not_found', 'n/a', 'na', '-', '']:
                return 0.0

            try:
                # Remove common non-numeric characters
                clean_value = value.replace(',', '').replace('(', '-').replace(')', '')
                clean_value = ''.join(c for c in clean_value if c.isdigit() or c in '.-')
                return float(clean_value) if clean_value else 0.0
            except (ValueError, TypeError):
                return 0.0

        return 0.0

    def extract_from_classification_manifest(self, manifest_path: str) -> Dict[str, Any]:
        """
        Extract financial data using classification manifest
        """
        logger.info(f"📋 Loading classification manifest: {manifest_path}")
        
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            # Get classified images from manifest
            classified_images = {}
            
            if "classifications" in manifest:
                for classification, details in manifest["classifications"].items():
                    if "full_paths" in details:
                        classified_images[classification] = details["full_paths"]
            
            logger.info(f"📂 Found {len(classified_images)} classification categories")
            for category, images in classified_images.items():
                logger.info(f"  📁 {category}: {len(images)} images")
            
            # Extract financial data using focused approach
            return self.extract_financial_data(classified_images)
            
        except Exception as e:
            logger.error(f"❌ Failed to load manifest: {e}")
            raise


# Test function
def test_focused_extractor():
    """Test the focused financial extractor"""
    print("🧪 Testing Focused Financial Extractor...")
    
    extractor = FocusedFinancialExtractor()
    
    print("\n✅ Focused Financial Extractor test completed!")
    print("🎯 Ready for separate Balance Sheet and Profit & Loss extraction!")


if __name__ == "__main__":
    test_focused_extractor()