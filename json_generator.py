"""
Financial JSON Generator - Maps Calculator Output to Final Structure
Author: Gangatharangurusamy
Description: Maps calculated financial metrics to the required JSON structure
Input: Calculator output with all financial metrics
Output: Final JSON structure ready for application use
"""

import logging
import json
import os
from typing import Dict, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialJSONGenerator:
    """
    Generate final JSON structure from calculated financial metrics
    """
    
    def __init__(self):
        """Initialize the JSON generator"""
        logger.info("✅ Financial JSON Generator initialized")
        logger.info("🎯 Ready to map calculator output to final JSON structure")

    def generate_financial_assumptions_json(self, financial_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate the final financial assumptions JSON structure
        """
        logger.info("📝 Generating financial assumptions JSON...")
        
        try:
            # Map calculator output to final structure
            financial_assumptions_json = {
                "pk": "",  # Will be set by application
                # "plant_name": financial_metrics.get("plant_name", "Unknown Company"),
                "sk": "financial_assumptions",
                "current_state": {
                    # Fixed assumptions
                    "annual_increase_rate": financial_metrics.get("annual_increase_rate", 1.05),
                    "salvage_rate": financial_metrics.get("salvage_rate", 0.1),
                    
                    # Country/currency specific
                    "transition_credit_base": financial_metrics.get("transition_credit_base", 833.9),
                    
                    # Company specific from calculations
                    "corporate_tax_rate_static": financial_metrics.get("corporate_tax_rate_static", 0.25),
                    "depreciation_rate": financial_metrics.get("depreciation_rate", 0.35),
                    "gross_block": financial_metrics.get("gross_block", 0),
                    "wdv_it": financial_metrics.get("wdv_it", 0),
                    
                    # WACC breakdown from calculations
                    "wacc_breakup": self._map_wacc_breakup(financial_metrics.get("wacc_breakup", {})),
                    
                    # Working capital
                    "working_capital_interest": financial_metrics.get("working_capital_interest", 0.12)
                }
            }
            
            # Add metadata for tracking
            # financial_assumptions_json["_metadata"] = {
            #     "generation_timestamp": datetime.now().isoformat(),
            #     "generated_by": "Gangatharangurusamy",
            #     "extracted_country": financial_metrics.get("extracted_country", "Unknown"),
            #     "extracted_currency": financial_metrics.get("extracted_currency", "Unknown"),
            #     "calculator_version": financial_metrics.get("calculation_metadata", {}).get("calculator_version", "1.0.0"),
            #     "data_source": "extracted_and_calculated"
            # }
            
            # Validate the generated JSON
            self._validate_json_structure(financial_assumptions_json)
            
            logger.info("✅ Financial assumptions JSON generated successfully")
            self._log_generation_summary(financial_assumptions_json)
            
            return financial_assumptions_json
            
        except Exception as e:
            logger.error(f"❌ Financial JSON generation failed: {e}")
            raise Exception(f"JSON generation failed: {e}")

    def _map_wacc_breakup(self, wacc_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Map WACC breakdown to the required structure
        """
        logger.info("⚖️ Mapping WACC breakdown...")
        
        mapped_wacc = {
            "cost_of_debt": float(wacc_data.get("cost_of_debt", 0.09)),
            "debt": float(wacc_data.get("debt", 0.5)),
            "equity": float(wacc_data.get("equity", 0.5)),
            "roe": float(wacc_data.get("roe", 0.12)),
            "tax_rate": float(wacc_data.get("tax_rate", 0.25)),
            "wacc": float(wacc_data.get("wacc", 0.10))
        }
        
        # Ensure debt + equity = 1.0 (within tolerance)
        debt_equity_sum = mapped_wacc["debt"] + mapped_wacc["equity"]
        if abs(debt_equity_sum - 1.0) > 0.01:  # More than 1% difference
            logger.warning(f"⚠️ Debt + Equity = {debt_equity_sum:.3f}, adjusting to sum to 1.0")
            # Normalize to ensure they sum to 1.0
            mapped_wacc["debt"] = mapped_wacc["debt"] / debt_equity_sum
            mapped_wacc["equity"] = mapped_wacc["equity"] / debt_equity_sum
        
        logger.info(f"💰 Cost of Debt: {mapped_wacc['cost_of_debt']:.2%}")
        logger.info(f"🏦 Debt Share: {mapped_wacc['debt']:.1%}")
        logger.info(f"📈 Equity Share: {mapped_wacc['equity']:.1%}")
        logger.info(f"📊 ROE: {mapped_wacc['roe']:.2%}")
        logger.info(f"⚖️ WACC: {mapped_wacc['wacc']:.2%}")
        
        return mapped_wacc

    def _validate_json_structure(self, financial_json: Dict[str, Any]) -> None:
        """
        Validate the generated JSON structure
        """
        logger.info("🔍 Validating JSON structure...")
        
        # Check required top-level fields
        required_fields = ["pk", "sk", "current_state"]
        for field in required_fields:
            if field not in financial_json:
                raise ValueError(f"Missing required field: {field}")
        
        # Check current_state structure
        current_state = financial_json["current_state"]
        required_current_state_fields = [
            "annual_increase_rate",
            "salvage_rate", 
            "transition_credit_base",
            "corporate_tax_rate_static",
            "depreciation_rate",
            "gross_block",
            "wdv_it",
            "wacc_breakup",
            "working_capital_interest"
        ]
        
        for field in required_current_state_fields:
            if field not in current_state:
                raise ValueError(f"Missing required current_state field: {field}")
        
        # Check WACC breakup structure
        wacc_breakup = current_state["wacc_breakup"]
        required_wacc_fields = ["cost_of_debt", "debt", "equity", "roe", "tax_rate", "wacc"]
        
        for field in required_wacc_fields:
            if field not in wacc_breakup:
                raise ValueError(f"Missing required WACC field: {field}")
        
        # Validate critical values
        if current_state["gross_block"] <= 0:
            raise ValueError("Gross block must be positive")
        
        if current_state["wdv_it"] <= 0:
            raise ValueError("WDV IT must be positive")
        
        logger.info("✅ JSON structure validation passed")

    def _log_generation_summary(self, financial_json: Dict[str, Any]) -> None:
        """
        Log generation summary
        """
        current_state = financial_json["current_state"]
        wacc_breakup = current_state["wacc_breakup"]
        metadata = financial_json.get("_metadata", {})
        
        logger.info("📊 FINANCIAL JSON GENERATION SUMMARY:")
        # logger.info(f"  🏭 Plant: {financial_json.get('plant_name', 'Unknown')}")
        logger.info(f"  🌍 Country: {metadata.get('extracted_country', 'Unknown')}")
        logger.info(f"  💱 Currency: {metadata.get('extracted_currency', 'Unknown')}")
        logger.info(f"  🏗️ Gross Block: {current_state.get('gross_block', 0):,.0f}")
        logger.info(f"  📊 WDV IT: {current_state.get('wdv_it', 0):,.0f}")
        logger.info(f"  🏛️ Tax Rate: {current_state.get('corporate_tax_rate_static', 0):.1%}")
        logger.info(f"  🏗️ Depreciation: {current_state.get('depreciation_rate', 0):.1%}")
        logger.info(f"  💰 Transition Credit: {current_state.get('transition_credit_base', 0):,.1f}")
        logger.info(f"  ⚖️ WACC: {wacc_breakup.get('wacc', 0):.2%}")
        logger.info(f"  💼 Working Capital Interest: {current_state.get('working_capital_interest', 0):.2%}")

    def create_complete_output_package(self, financial_metrics: Dict[str, Any], 
                                     output_dir: str, pdf_path: str) -> Dict[str, Any]:
        """
        Create complete output package with all files
        """
        logger.info("📦 Creating complete output package...")
        
        try:
            # Generate the financial assumptions JSON
            financial_json = self.generate_financial_assumptions_json(financial_metrics)
            
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            # Extract filename from pdf_path
            pdf_filename = os.path.basename(pdf_path)
            base_name = os.path.splitext(pdf_filename)[0]
            
            # Define output files
            output_files = {
                "financial_assumptions_json": os.path.join(output_dir, f"{base_name}_financial_assumptions.json"),
                "financial_metrics_json": os.path.join(output_dir, f"{base_name}_financial_metrics.json"),
                "summary_report": os.path.join(output_dir, f"{base_name}_summary_report.txt")
            }
            
            # Save financial assumptions JSON
            with open(output_files["financial_assumptions_json"], 'w', encoding='utf-8') as f:
                json.dump(financial_json, f, indent=2, ensure_ascii=False)
            
            # Save detailed financial metrics
            with open(output_files["financial_metrics_json"], 'w', encoding='utf-8') as f:
                json.dump(financial_metrics, f, indent=2, ensure_ascii=False)
            
            # Generate and save summary report
            summary_report = self._generate_summary_report(financial_json, financial_metrics)
            with open(output_files["summary_report"], 'w', encoding='utf-8') as f:
                f.write(summary_report)
            
            # Create package result with the key that main.py expects
            package_result = {
                "success": True,
                "financial_assumptions_json": financial_json,
                "output_files": output_files,
                "final_json_path": output_files["financial_assumptions_json"],  # Key that main.py expects
                "package_metadata": {
                    "generation_timestamp": datetime.now().isoformat(),
                    "generated_by": "Gangatharangurusamy",
                    "pdf_source": pdf_filename,
                    "pdf_path": pdf_path,
                    "output_directory": output_dir,
                    "files_created": len(output_files)
                }
            }
            
            logger.info("✅ Complete output package created successfully")
            logger.info(f"📁 Output directory: {output_dir}")
            logger.info(f"📄 Files created: {len(output_files)}")
            logger.info(f"📂 Final JSON path: {output_files['financial_assumptions_json']}")
            
            return package_result
            
        except Exception as e:
            logger.error(f"❌ Complete output package creation failed: {e}")
            raise Exception(f"Output package creation failed: {e}")

    def _generate_summary_report(self, financial_json: Dict[str, Any], 
                               financial_metrics: Dict[str, Any]) -> str:
        """
        Generate a human-readable summary report
        """
        current_state = financial_json.get("current_state", {})
        wacc_breakup = current_state.get("wacc_breakup", {})
        metadata = financial_json.get("_metadata", {})
        
        report_lines = [
            "="*80,
            "FINANCIAL ASSUMPTIONS SUMMARY REPORT",
            "="*80,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC",
            f"Generated by: Gangatharangurusamy",
            # f"Plant Name: {financial_json.get('plant_name', 'Unknown')}",
            f"Country: {metadata.get('extracted_country', 'Unknown')}",
            f"Currency: {metadata.get('extracted_currency', 'Unknown')}",
            "",
            "FINANCIAL ASSUMPTIONS:",
            "-"*50,
            f"Annual Increase Rate: {current_state.get('annual_increase_rate', 0):.1%}",
            f"Salvage Rate: {current_state.get('salvage_rate', 0):.1%}",
            f"Transition Credit Base: {current_state.get('transition_credit_base', 0):,.2f}",
            f"Corporate Tax Rate: {current_state.get('corporate_tax_rate_static', 0):.1%}",
            f"Depreciation Rate: {current_state.get('depreciation_rate', 0):.1%}",
            f"Gross Block: {current_state.get('gross_block', 0):,.0f}",
            f"WDV IT: {current_state.get('wdv_it', 0):,.0f}",
            f"Working Capital Interest: {current_state.get('working_capital_interest', 0):.2%}",
            "",
            "WACC BREAKDOWN:",
            "-"*50,
            f"Cost of Debt: {wacc_breakup.get('cost_of_debt', 0):.2%}",
            f"Debt Ratio: {wacc_breakup.get('debt', 0):.1%}",
            f"Equity Ratio: {wacc_breakup.get('equity', 0):.1%}",
            f"ROE: {wacc_breakup.get('roe', 0):.2%}",
            f"Tax Rate: {wacc_breakup.get('tax_rate', 0):.1%}",
            f"WACC: {wacc_breakup.get('wacc', 0):.2%}",
            "",
            "CALCULATION DETAILS:",
            "-"*50,
            f"Total Debt: {financial_metrics.get('calculated_values', {}).get('total_debt', 0):,.0f}",
            f"Debt-to-Equity Ratio: {financial_metrics.get('calculated_values', {}).get('debt_equity_ratio', 0):.2f}",
            f"Equity Multiplier: {financial_metrics.get('calculated_values', {}).get('equity_multiplier', 0):.2f}",
            "",
            "GENERATION METADATA:",
            "-"*50,
            f"Calculator Version: {metadata.get('calculator_version', 'Unknown')}",
            f"Data Source: {metadata.get('data_source', 'Unknown')}",
            f"Extraction Model: GPT-4o",
            f"Generation Model: Focused extraction + calculation",
            "="*80
        ]
        
        return "\n".join(report_lines)

    def export_to_file(self, financial_json: Dict[str, Any], output_path: str) -> bool:
        """
        Export JSON to file
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(financial_json, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ JSON exported to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ JSON export failed: {e}")
            return False


# Test function
def test_json_generator():
    """Test the financial JSON generator"""
    print("🧪 Testing Financial JSON Generator...")
    
    generator = FinancialJSONGenerator()
    
    # Test with sample calculator output
    sample_metrics = {
        # "plant_name": "Test Company SOC Ltd",
        "extracted_country": "South Africa",
        "extracted_currency": "ZAR",
        "gross_block": 603093000000,
        "wdv_it": 603093000000,
        "annual_increase_rate": 1.05,
        "salvage_rate": 0.1,
        "corporate_tax_rate_static": 0.28,
        "depreciation_rate": 0.50,
        "transition_credit_base": 180.0,
        "wacc_breakup": {
            "cost_of_debt": 0.1029,
            "debt": 0.5735,
            "equity": 0.4265,
            "roe": -0.0912,
            "tax_rate": 0.28,
            "wacc": 0.0952
        },
        "working_capital_interest": 0.10189
    }
    
    try:
        result = generator.generate_financial_assumptions_json(sample_metrics)
        print("\n✅ JSON Generator test completed successfully!")
        # print(f"🎯 Plant: {result.get('plant_name', 'Unknown')}")
        print(f"🎯 WACC: {result['current_state']['wacc_breakup']['wacc']:.2%}")
        
        return result
        
    except Exception as e:
        print(f"❌ JSON Generator test failed: {e}")
        return None


if __name__ == "__main__":
    test_json_generator()