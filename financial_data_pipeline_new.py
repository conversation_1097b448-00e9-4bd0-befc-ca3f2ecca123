import json
import re
from financial_model_new import ask_gpt4o_vision
# from prompt_scehma_new import assets_prompt, cashflows_prompt, revenue_expenses_prompt,credit_rating_prompt

import os


def extract_json_from_response(response_text):
    match = re.search(r'```json(.*?)```', response_text, re.DOTALL)
    if match:
        json_str = match.group(1).strip()
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print("Failed to parse JSON:", e)
    else:
        print("JSON block not found.")
    return {}

def flatten_nested_keys(data):
    """Flattens nested sections like 'cashflows': {'cashflows': [...]} and
    unwraps 'credit_rating' structure into top-level keys."""
    flattened = {}
    for key, value in data.items():
        # Special handling for credit_rating
        if key == "credit_rating" and isinstance(value, dict):
            if "credit_rating" in value:
                flattened["credit_rating"] = value["credit_rating"]
            if "credit_rating_note" in value:
                flattened["credit_rating_note"] = value["credit_rating_note"]
            if "currency" in value:
                flattened["currency"] = value["currency"]
        # Generic flattening if key is nested under itself
        elif isinstance(value, dict) and key in value:
            flattened[key] = value[key]
        else:
            flattened[key] = value
    return flattened


def get_all_images_sorted(folder_path):
    if not folder_path or not os.path.isdir(folder_path):
        return []
    return sorted([
        os.path.join(folder_path, f)
        for f in os.listdir(folder_path)
        if f.lower().endswith((".png", ".jpg", ".jpeg"))
    ])


def extract_consolidated_financials(
    balance_sheet_path,
    profit_and_loss_path,
    cash_flows_path,
    credit_rating_path,
    assets_prompt,
    cashflows_prompt,
    revenue_expenses_prompt,
    credit_rating_prompt,
    output_file_path
):
    consolidated_data = {}

    balance_images = get_all_images_sorted(balance_sheet_path)
    profit_loss_images = get_all_images_sorted(profit_and_loss_path)
    cashflow_images = get_all_images_sorted(cash_flows_path)
    credit_images = get_all_images_sorted(credit_rating_path)

    print(f"🟢 Processing {len(balance_images)} balance sheet image(s)")
    response_assets = ask_gpt4o_vision(balance_images, assets_prompt)
    consolidated_data["assets"] = extract_json_from_response(response_assets)

    print(f"🟢 Processing {len(cashflow_images)} cashflow image(s)")
    response_cashflow = ask_gpt4o_vision(cashflow_images, cashflows_prompt)
    consolidated_data["cashflows"] = extract_json_from_response(response_cashflow)

    print(f"🟢 Processing {len(profit_loss_images)} P&L image(s)")
    response_revenue_expenses = ask_gpt4o_vision(profit_loss_images, revenue_expenses_prompt)
    consolidated_data["revenue_expenses"] = extract_json_from_response(response_revenue_expenses)

    print(f"🟢 Processing {len(credit_images)} credit rating image(s)")
    response_credit_rating = ask_gpt4o_vision(credit_images, credit_rating_prompt)
    consolidated_data["credit_rating"] = extract_json_from_response(response_credit_rating)

    final_output = flatten_nested_keys(consolidated_data)

    with open(output_file_path, "w") as f:
        json.dump(final_output, f, indent=2)

    print(f"\n✅ Consolidated output saved to {output_file_path}")
