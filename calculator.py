"""
Financial Metrics Calculator - Processes Balance Sheet & Profit Loss Data
Author: Gangatharangurusamy
Date: 2025-07-21
Description: Calculate all financial metrics from extracted Balance Sheet and P&L data
Input: Two separate JSON outputs from focused extraction
Output: Complete financial metrics for JSON generation
Enhanced: Added more countries and currency exchange API
"""

import logging
import json
import requests
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import math

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialMetricsCalculator:
    """
    Calculate comprehensive financial metrics from Balance Sheet and Profit & Loss data
    Enhanced with more countries and currency exchange API
    """
    
    # Enhanced Country-based depreciation rates (added more countries)
    DEPRECIATION_RATES = {
        # Existing countries
        "India": 0.40,          # 40% for India
        "South Africa": 0.50,   # 50% for South Africa  
        "United States": 0.35,  # 35% for United States
        "Germany": 0.30,        # 30% for Germany
        
        # Added countries
        "United Kingdom": 0.25,  # 25% for UK
        "Canada": 0.30,         # 30% for Canada
        "Australia": 0.375,     # 37.5% for Australia
        "Japan": 0.20,          # 20% for Japan
        "China": 0.25,          # 25% for China
        "France": 0.31,         # 31% for France
        "Italy": 0.24,          # 24% for Italy
        "Spain": 0.25,          # 25% for Spain
        "Netherlands": 0.25,    # 25% for Netherlands
        "Sweden": 0.20,         # 20% for Sweden
        "Norway": 0.22,         # 22% for Norway
        "Denmark": 0.22,        # 22% for Denmark
        "Finland": 0.20,        # 20% for Finland
        "Switzerland": 0.20,    # 20% for Switzerland
        "Austria": 0.25,        # 25% for Austria
        "Belgium": 0.29,        # 29% for Belgium
        "Brazil": 0.34,         # 34% for Brazil
        "Mexico": 0.30,         # 30% for Mexico
        "Argentina": 0.35,      # 35% for Argentina
        "Chile": 0.27,          # 27% for Chile
        "Russia": 0.20,         # 20% for Russia
        "South Korea": 0.25,    # 25% for South Korea
        "Singapore": 0.17,      # 17% for Singapore
        "Malaysia": 0.24,       # 24% for Malaysia
        "Thailand": 0.20,       # 20% for Thailand
        "Indonesia": 0.22,      # 22% for Indonesia
        "Philippines": 0.30,    # 30% for Philippines
        "Vietnam": 0.20,        # 20% for Vietnam
        "UAE": 0.09,            # 9% for UAE
        "Saudi Arabia": 0.20,   # 20% for Saudi Arabia
        "Egypt": 0.225,         # 22.5% for Egypt
        "Nigeria": 0.30,        # 30% for Nigeria
        "Kenya": 0.30,          # 30% for Kenya
        "Ghana": 0.25,          # 25% for Ghana
        "New Zealand": 0.28,    # 28% for New Zealand
        "Ireland": 0.125,       # 12.5% for Ireland
        "Luxembourg": 0.24,     # 24% for Luxembourg
        "Portugal": 0.21,       # 21% for Portugal
        "Israel": 0.23,         # 23% for Israel
        "Turkey": 0.20,         # 20% for Turkey
        "Poland": 0.19,         # 19% for Poland
        "Czech Republic": 0.19, # 19% for Czech Republic
        "Hungary": 0.09,        # 9% for Hungary
        "Slovakia": 0.21,       # 21% for Slovakia
        "Slovenia": 0.19,       # 19% for Slovenia
        "Croatia": 0.18,        # 18% for Croatia
        "Romania": 0.16,        # 16% for Romania
        "Bulgaria": 0.10,       # 10% for Bulgaria
        "Estonia": 0.20,        # 20% for Estonia
        "Latvia": 0.20,         # 20% for Latvia
        "Lithuania": 0.15,      # 15% for Lithuania
        
        "Unknown": 0.35         # Default fallback
    }
    
    # Enhanced Country-based tax rates (added more countries)
    TAX_RATES = {
        # Existing countries
        "India": 0.264,         # 26.4% for India
        "South Africa": 0.28,   # 28% for South Africa
        "United States": 0.21,  # 21% for United States
        "Germany": 0.32,        # 32% for Germany
        
        # Added countries
        "United Kingdom": 0.25,  # 25% for UK
        "Canada": 0.27,         # 27% for Canada
        "Australia": 0.30,      # 30% for Australia
        "Japan": 0.31,          # 31% for Japan
        "China": 0.25,          # 25% for China
        "France": 0.32,         # 32% for France
        "Italy": 0.27,          # 27% for Italy
        "Spain": 0.25,          # 25% for Spain
        "Netherlands": 0.25,    # 25% for Netherlands
        "Sweden": 0.20,         # 20% for Sweden
        "Norway": 0.22,         # 22% for Norway
        "Denmark": 0.22,        # 22% for Denmark
        "Finland": 0.20,        # 20% for Finland
        "Switzerland": 0.19,    # 19% for Switzerland
        "Austria": 0.25,        # 25% for Austria
        "Belgium": 0.29,        # 29% for Belgium
        "Brazil": 0.34,         # 34% for Brazil
        "Mexico": 0.30,         # 30% for Mexico
        "Argentina": 0.35,      # 35% for Argentina
        "Chile": 0.27,          # 27% for Chile
        "Russia": 0.20,         # 20% for Russia
        "South Korea": 0.25,    # 25% for South Korea
        "Singapore": 0.17,      # 17% for Singapore
        "Malaysia": 0.24,       # 24% for Malaysia
        "Thailand": 0.20,       # 20% for Thailand
        "Indonesia": 0.22,      # 22% for Indonesia
        "Philippines": 0.30,    # 30% for Philippines
        "Vietnam": 0.20,        # 20% for Vietnam
        "UAE": 0.09,            # 9% for UAE
        "Saudi Arabia": 0.20,   # 20% for Saudi Arabia
        "Egypt": 0.225,         # 22.5% for Egypt
        "Nigeria": 0.30,        # 30% for Nigeria
        "Kenya": 0.30,          # 30% for Kenya
        "Ghana": 0.25,          # 25% for Ghana
        "New Zealand": 0.28,    # 28% for New Zealand
        "Ireland": 0.125,       # 12.5% for Ireland
        "Luxembourg": 0.24,     # 24% for Luxembourg
        "Portugal": 0.21,       # 21% for Portugal
        "Israel": 0.23,         # 23% for Israel
        "Turkey": 0.20,         # 20% for Turkey
        "Poland": 0.19,         # 19% for Poland
        "Czech Republic": 0.19, # 19% for Czech Republic
        "Hungary": 0.09,        # 9% for Hungary
        "Slovakia": 0.21,       # 21% for Slovakia
        "Slovenia": 0.19,       # 19% for Slovenia
        "Croatia": 0.18,        # 18% for Croatia
        "Romania": 0.16,        # 16% for Romania
        "Bulgaria": 0.10,       # 10% for Bulgaria
        "Estonia": 0.20,        # 20% for Estonia
        "Latvia": 0.20,         # 20% for Latvia
        "Lithuania": 0.15,      # 15% for Lithuania
        
        "Unknown": 0.25         # 25% default
    }
    
    # Enhanced Country-based transition credits (added more currencies)
    TRANSITION_CREDITS = {
        # Existing currencies
        "INR": 833.9,      # India - Renewable energy transition
        "ZAR": 180.0,      # South Africa - Coal to renewable transition
        "USD": 10.0,       # United States - Clean energy transition
        "EUR": 8.5,        # European Union - Green Deal transition
        
        # Added currencies
        "GBP": 12.0,       # United Kingdom - Green finance initiative
        "CAD": 13.5,       # Canada - Clean technology transition
        "AUD": 15.0,       # Australia - Renewable energy scheme
        "JPY": 1100.0,     # Japan - Green transformation
        "CNY": 65.0,       # China - Carbon neutrality program
        "CHF": 9.0,        # Switzerland - Energy strategy
        "SEK": 85.0,       # Sweden - Green deal
        "NOK": 90.0,       # Norway - Energy transition
        "DKK": 60.0,       # Denmark - Green investment
        "BRL": 50.0,       # Brazil - Green development
        "MXN": 200.0,      # Mexico - Clean energy transition
        "KRW": 12000.0,    # South Korea - Green New Deal
        "SGD": 14.0,       # Singapore - Green finance
        "AED": 36.0,       # UAE - Clean energy strategy
        "SAR": 37.5,       # Saudi Arabia - Green initiative
        "RUB": 750.0,      # Russia - Energy efficiency
        "TRY": 85.0,       # Turkey - Green development
        "PLN": 40.0,       # Poland - Energy transition
        "CZK": 220.0,      # Czech Republic - Green deal
        "HUF": 3500.0,     # Hungary - Green transition
        "THB": 330.0,      # Thailand - Energy transition
        "MYR": 42.0,       # Malaysia - Green technology
        "IDR": 145000.0,   # Indonesia - Energy transition
        "PHP": 500.0,      # Philippines - Clean energy
        "VND": 230000.0,   # Vietnam - Green development
        "EGP": 155.0,      # Egypt - Renewable energy
        "NGN": 4100.0,     # Nigeria - Energy transition
        "KES": 1100.0,     # Kenya - Green energy
        "GHS": 60.0,       # Ghana - Renewable energy
        "NZD": 16.0,       # New Zealand - Climate action
        "ILS": 32.0,       # Israel - Green innovation
        "RON": 42.0,       # Romania - Green transition
        "BGN": 17.0,       # Bulgaria - Energy efficiency
        
        "Unknown": 10.0    # Default fallback
    }
    
    # Currency Exchange API configuration
    EXCHANGE_API_BASE_URL = "https://api.exchangerate-api.com/v4/latest/"
    FALLBACK_EXCHANGE_RATES = {
        "USD": 1.0,
        "EUR": 0.85,
        "GBP": 0.73,
        "JPY": 110.0,
        "INR": 74.5,
        "ZAR": 14.8,
        "CAD": 1.25,
        "AUD": 1.35,
        "CHF": 0.92,
        "CNY": 6.45
    }
    
    def __init__(self):
        """Initialize the financial metrics calculator"""
        logger.info("✅ Financial Metrics Calculator initialized")
        logger.info(f"🌍 Depreciation rates configured for {len(self.DEPRECIATION_RATES)} countries")
        logger.info(f"🏛️ Tax rates configured for {len(self.TAX_RATES)} countries")
        logger.info(f"💰 Transition credits configured for {len(self.TRANSITION_CREDITS)} currencies")
        logger.info(f"💱 Currency exchange API configured: {self.EXCHANGE_API_BASE_URL}")

    def get_exchange_rate(self, from_currency: str, to_currency: str = "USD") -> float:
        """
        Get exchange rate from API with fallback to static rates
        """
        logger.info(f"💱 Getting exchange rate: {from_currency} to {to_currency}")
        
        try:
            # Try to get from API
            url = f"{self.EXCHANGE_API_BASE_URL}{from_currency}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if to_currency in data.get("rates", {}):
                    exchange_rate = data["rates"][to_currency]
                    logger.info(f"✅ API exchange rate: 1 {from_currency} = {exchange_rate} {to_currency}")
                    return exchange_rate
                else:
                    logger.warning(f"⚠️ {to_currency} not found in API response")
            else:
                logger.warning(f"⚠️ API request failed with status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ API request failed: {e}")
        except Exception as e:
            logger.warning(f"⚠️ Exchange rate API error: {e}")
        
        # Fallback to static rates
        logger.info("📊 Using fallback exchange rates")
        
        from_rate = self.FALLBACK_EXCHANGE_RATES.get(from_currency, 1.0)
        to_rate = self.FALLBACK_EXCHANGE_RATES.get(to_currency, 1.0)
        
        if from_currency == to_currency:
            exchange_rate = 1.0
        else:
            # Convert through USD
            exchange_rate = to_rate / from_rate
        
        logger.info(f"📊 Fallback exchange rate: 1 {from_currency} = {exchange_rate} {to_currency}")
        return exchange_rate

    def convert_currency(self, amount: float, from_currency: str, to_currency: str = "USD") -> Dict[str, Any]:
        """
        Convert currency amount with metadata
        """
        if from_currency == to_currency:
            return {
                "original_amount": amount,
                "converted_amount": amount,
                "from_currency": from_currency,
                "to_currency": to_currency,
                "exchange_rate": 1.0,
                "conversion_timestamp": datetime.now().isoformat(),
                "rate_source": "no_conversion_needed"
            }
        
        exchange_rate = self.get_exchange_rate(from_currency, to_currency)
        converted_amount = amount * exchange_rate
        
        return {
            "original_amount": amount,
            "converted_amount": converted_amount,
            "from_currency": from_currency,
            "to_currency": to_currency,
            "exchange_rate": exchange_rate,
            "conversion_timestamp": datetime.now().isoformat(),
            "rate_source": "api_or_fallback"
        }

    def calculate_financial_metrics(self, balance_sheet_data: Dict[str, Any], 
                                  profit_loss_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate comprehensive financial metrics from extracted data
        """
        logger.info("🧮 Starting financial metrics calculation...")
        
        try:
            # Extract balance sheet values
            bs = balance_sheet_data.get("balance_sheet", {})
            pl = profit_loss_data.get("profit_loss", {})
            
            # Basic extracted values
            extracted_values = self._extract_basic_values(bs, pl)
            
            # Calculate debt metrics
            debt_metrics = self._calculate_debt_metrics(extracted_values)
            
            # Calculate WACC components
            wacc_metrics = self._calculate_wacc_metrics(extracted_values, debt_metrics)
            
            # Get country-specific rates (enhanced)
            country_rates = self._get_country_specific_rates(bs)
            
            # Calculate working capital interest (placeholder - needs notes data)
            working_capital_interest = self._calculate_working_capital_interest(extracted_values)
            
            # Get currency information
            currency_info = self._get_currency_information(bs)
            
            # Assemble final financial metrics
            financial_metrics = {
                "calculation_metadata": {
                    "calculation_timestamp": datetime.now().isoformat(),
                    "calculator_version": "1.1.0",  # Updated version
                    "extracted_country": bs.get("country", "Unknown"),
                    "extracted_currency": bs.get("currency", "Unknown"),
                    "multiplier_bs": bs.get("multiplier_detected", "unknown"),
                    "multiplier_pl": pl.get("multiplier_detected", "unknown"),
                    "countries_supported": len(self.DEPRECIATION_RATES),
                    "currencies_supported": len(self.TRANSITION_CREDITS)
                },
                
                # Company identification
                "plant_name": bs.get("plant_name", "Unknown Company"),
                "extracted_country": bs.get("country", "Unknown"),
                "extracted_currency": bs.get("currency", "Unknown"),
                
                # Currency information
                "currency_information": currency_info,
                
                # Balance sheet values (direct mapping)
                "gross_block": extracted_values["ppe"],
                "wdv_it": extracted_values["ppe"],  # Same as PPE
                
                # Fixed assumptions
                "annual_increase_rate": 1.05,  # 5% annual increase
                "salvage_rate": 0.1,           # 10% salvage rate
                
                # Country-specific rates (enhanced)
                "corporate_tax_rate_static": country_rates["tax_rate"],
                "depreciation_rate": country_rates["depreciation_rate"],
                "transition_credit_base": country_rates["transition_credit"],
                
                # Calculated WACC breakdown
                "wacc_breakup": {
                    "cost_of_debt": wacc_metrics["cost_of_debt"],
                    "debt": debt_metrics["debt_share"],
                    "equity": debt_metrics["equity_share"],
                    "roe": wacc_metrics["roe"],
                    "tax_rate": country_rates["tax_rate"],
                    "wacc": wacc_metrics["wacc"]
                },
                
                # Working capital
                "working_capital_interest": working_capital_interest,
                
                # Raw extracted data for reference
                "raw_balance_sheet": bs,
                "raw_profit_loss": pl,
                
                # Calculated intermediates
                "calculated_values": {
                    "total_debt": debt_metrics["total_debt"],
                    "debt_equity_ratio": debt_metrics["debt_equity_ratio"],
                    "equity_multiplier": debt_metrics["equity_multiplier"]
                }
            }
            
            # Log calculation summary
            self._log_calculation_summary(financial_metrics)
            
            logger.info("✅ Financial metrics calculation completed successfully")
            return financial_metrics
            
        except Exception as e:
            logger.error(f"❌ Financial metrics calculation failed: {e}")
            raise Exception(f"Calculation failed: {e}")

    def _extract_basic_values(self, bs: Dict[str, Any], pl: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and validate basic financial values - ENHANCED VERSION"""
        logger.info("📊 Extracting basic financial values...")

        # Balance Sheet values - ENHANCED: Safe numeric conversion
        ppe = self._safe_numeric_conversion(bs.get("Property, plant and equipment", 0))
        current_assets = self._safe_numeric_conversion(bs.get("current_assets", 0))
        current_liabilities = self._safe_numeric_conversion(bs.get("current_liabilities", 0))
        short_term_debt = self._safe_numeric_conversion(bs.get("short_term_borrowings", 0))
        long_term_debt = self._safe_numeric_conversion(bs.get("Long_Term_borrowing", 0))
        total_equity = self._safe_numeric_conversion(bs.get("total_equity", 0))
        cash = self._safe_numeric_conversion(bs.get("cash_and_cash_equivalents", 0))

        # Profit & Loss values - ENHANCED: Safe numeric conversion
        revenue = self._safe_numeric_conversion(pl.get("revenue", 0))
        finance_cost = self._safe_numeric_conversion(pl.get("finance_cost", 0))
        profit_before_tax = self._safe_numeric_conversion(pl.get("profit_before_tax", 0))
        net_income = self._safe_numeric_conversion(pl.get("net_income", 0))
        
        # Calculate total debt - NOW SAFE: 0 + 0 = 0 (not None + None = Error)
        total_debt = short_term_debt + long_term_debt
        
        # Validate critical values
        if ppe <= 0:
            logger.warning("⚠️ PPE is zero or negative, using minimum value")
            ppe = 1000000  # 1 million minimum
        
        if total_equity <= 0:
            logger.warning("⚠️ Total equity is zero or negative, using minimum value")
            total_equity = 1000000  # 1 million minimum
        
        if total_debt <= 0:
            logger.warning("⚠️ Total debt is zero, using minimum value")
            total_debt = 1000000  # 1 million minimum
        
        extracted_values = {
            "ppe": ppe,
            "current_assets": current_assets,
            "current_liabilities": current_liabilities,
            "short_term_debt": short_term_debt,
            "long_term_debt": long_term_debt,
            "total_debt": total_debt,
            "total_equity": total_equity,
            "cash": cash,
            "revenue": revenue,
            "finance_cost": finance_cost,
            "profit_before_tax": profit_before_tax,
            "net_income": net_income
        }
        
        logger.info(f"💰 PPE: {ppe:,.0f}")
        logger.info(f"🏦 Total Debt: {total_debt:,.0f}")
        logger.info(f"📈 Total Equity: {total_equity:,.0f}")
        logger.info(f"💸 Finance Cost: {finance_cost:,.0f}")
        logger.info(f"📊 Net Income: {net_income:,.0f}")
        
        return extracted_values

    def _safe_numeric_conversion(self, value) -> float:
        """Safely convert value to numeric, handling strings and other types"""
        if value is None:
            return 0.0

        if isinstance(value, (int, float)):
            return float(value)

        if isinstance(value, str):
            # Handle special cases
            if value.lower() in ['not_found', 'n/a', 'na', '-', '', 'nil', 'null']:
                return 0.0

            try:
                # Remove common non-numeric characters and currency symbols
                clean_value = value.replace(',', '').replace('(', '-').replace(')', '')
                clean_value = clean_value.replace('₹', '').replace('$', '').replace('€', '').replace('£', '')
                clean_value = ''.join(c for c in clean_value if c.isdigit() or c in '.-')
                return float(clean_value) if clean_value else 0.0
            except (ValueError, TypeError):
                logger.warning(f"⚠️ Could not convert '{value}' to numeric, using 0.0")
                return 0.0

        return 0.0

    def _calculate_debt_metrics(self, values: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate debt-related metrics"""
        logger.info("🏦 Calculating debt metrics...")
        
        total_debt = values["total_debt"]
        total_equity = values["total_equity"]
        total_capital = total_debt + total_equity
        
        # Calculate debt and equity shares
        debt_share = total_debt / total_capital if total_capital > 0 else 0.5
        equity_share = total_equity / total_capital if total_capital > 0 else 0.5
        
        # Ensure shares sum to 1.0
        if debt_share + equity_share != 1.0:
            total_shares = debt_share + equity_share
            if total_shares > 0:
                debt_share = debt_share / total_shares
                equity_share = equity_share / total_shares
            else:
                debt_share = 0.5
                equity_share = 0.5
        
        # Calculate other debt metrics
        debt_equity_ratio = total_debt / total_equity if total_equity > 0 else 1.0
        equity_multiplier = total_capital / total_equity if total_equity > 0 else 2.0
        
        debt_metrics = {
            "total_debt": total_debt,
            "debt_share": debt_share,
            "equity_share": equity_share,
            "debt_equity_ratio": debt_equity_ratio,
            "equity_multiplier": equity_multiplier
        }
        
        logger.info(f"⚖️ Debt Share: {debt_share:.1%}")
        logger.info(f"📈 Equity Share: {equity_share:.1%}")
        logger.info(f"📊 Debt-to-Equity Ratio: {debt_equity_ratio:.2f}")
        
        return debt_metrics

    def _calculate_wacc_metrics(self, values: Dict[str, Any], debt_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate WACC components"""
        logger.info("⚖️ Calculating WACC metrics...")
        
        # Cost of Debt = Finance Cost / Total Debt
        finance_cost = values["finance_cost"]
        total_debt = debt_metrics["total_debt"]
        
        cost_of_debt = finance_cost / total_debt if total_debt > 0 else 0.08
        
        # Convert to percentage and validate
        if cost_of_debt > 1.0:  # If > 100%, likely already in percentage form
            cost_of_debt = cost_of_debt / 100.0
        
        # Reasonable bounds for cost of debt (2% to 20%)
        cost_of_debt = max(0.02, min(cost_of_debt, 0.20))
        
        # ROE = Net Income / Total Equity
        net_income = values["net_income"]
        total_equity = values["total_equity"]
        
        roe = net_income / total_equity if total_equity > 0 else 0.12
        
        # Convert to percentage if needed and validate
        if abs(roe) > 1.0 and abs(roe) < 100:  # Likely in percentage form
            roe = roe / 100.0
        
        # Reasonable bounds for ROE (-50% to 50%)
        roe = max(-0.50, min(roe, 0.50))
        
        wacc_metrics = {
            "cost_of_debt": cost_of_debt,
            "roe": roe,
            "wacc": 0.0  # Will be calculated below
        }
        
        logger.info(f"💰 Cost of Debt: {cost_of_debt:.2%}")
        logger.info(f"📈 ROE: {roe:.2%}")
        
        return wacc_metrics

    def _get_country_specific_rates(self, bs: Dict[str, Any]) -> Dict[str, Any]:
        """Get country-specific rates for depreciation, tax, and transition credit (Enhanced)"""
        logger.info("🌍 Getting country-specific rates...")
        
        country = bs.get("country", "Unknown")
        currency = bs.get("currency", "Unknown")
        
        # Get depreciation rate based on country (enhanced)
        depreciation_rate = self.DEPRECIATION_RATES.get(country, self.DEPRECIATION_RATES["Unknown"])
        
        # Get transition credit based on currency (enhanced)
        transition_credit = self.TRANSITION_CREDITS.get(currency, self.TRANSITION_CREDITS["Unknown"])
        
        # Get tax rate based on country (enhanced)
        tax_rate = self.TAX_RATES.get(country, self.TAX_RATES["Unknown"])
        
        country_rates = {
            "depreciation_rate": depreciation_rate,
            "tax_rate": tax_rate,
            "transition_credit": transition_credit
        }
        
        logger.info(f"🏗️ Depreciation Rate ({country}): {depreciation_rate:.1%}")
        logger.info(f"🏛️ Tax Rate ({country}): {tax_rate:.1%}")
        logger.info(f"💰 Transition Credit ({currency}): {transition_credit:,.1f}")
        
        return country_rates

    def _get_currency_information(self, bs: Dict[str, Any]) -> Dict[str, Any]:
        """Get currency information with exchange rates"""
        logger.info("💱 Getting currency information...")
        
        currency = bs.get("currency", "Unknown")
        
        if currency == "Unknown":
            return {
                "local_currency": "Unknown",
                "usd_exchange_rate": 1.0,
                "eur_exchange_rate": 1.0,
                "rate_timestamp": datetime.now().isoformat(),
                "rate_source": "unknown_currency"
            }
        
        # Get exchange rates
        try:
            usd_rate = self.get_exchange_rate(currency, "USD")
            eur_rate = self.get_exchange_rate(currency, "EUR")
            
            currency_info = {
                "local_currency": currency,
                "usd_exchange_rate": usd_rate,
                "eur_exchange_rate": eur_rate,
                "rate_timestamp": datetime.now().isoformat(),
                "rate_source": "api_with_fallback"
            }
            
            logger.info(f"💱 Currency: {currency}")
            logger.info(f"💵 1 {currency} = {usd_rate:.4f} USD")
            logger.info(f"💶 1 {currency} = {eur_rate:.4f} EUR")
            
            return currency_info
            
        except Exception as e:
            logger.warning(f"⚠️ Currency information failed: {e}")
            return {
                "local_currency": currency,
                "usd_exchange_rate": 1.0,
                "eur_exchange_rate": 1.0,
                "rate_timestamp": datetime.now().isoformat(),
                "rate_source": "error_fallback"
            }

    def _calculate_working_capital_interest(self, values: Dict[str, Any]) -> float:
        """Calculate working capital interest (placeholder - needs notes data)"""
        logger.info("💼 Calculating working capital interest...")
        
        # Placeholder calculation - in real implementation, this would use notes data
        # Formula: Annual cost linked to short-term working capital funding / short-term borrowing
        
        short_term_debt = values["short_term_debt"]
        finance_cost = values["finance_cost"]
        
        if short_term_debt > 0:
            # Estimate working capital portion (typically 20-30% of finance cost)
            estimated_wc_cost = finance_cost * 0.25  # 25% assumption
            working_capital_interest = estimated_wc_cost / short_term_debt
            
            # Reasonable bounds (5% to 25%)
            working_capital_interest = max(0.05, min(working_capital_interest, 0.25))
        else:
            working_capital_interest = 0.12  # 12% default
        
        logger.info(f"💼 Working Capital Interest: {working_capital_interest:.2%}")
        logger.warning("⚠️ Working capital interest is estimated - needs notes data for exact calculation")
        
        return working_capital_interest

    def _calculate_final_wacc(self, wacc_metrics: Dict[str, Any], debt_metrics: Dict[str, Any], 
                            country_rates: Dict[str, Any]) -> float:
        """Calculate final WACC using the standard formula"""
        logger.info("⚖️ Calculating final WACC...")
        
        # WACC = E/(E+D) × Re + D/(E+D) × Rd × (1-Tc)
        # Where: E = Equity, D = Debt, Re = ROE, Rd = Cost of Debt, Tc = Tax Rate
        
        equity_share = debt_metrics["equity_share"]
        debt_share = debt_metrics["debt_share"]
        roe = wacc_metrics["roe"]
        cost_of_debt = wacc_metrics["cost_of_debt"]
        tax_rate = country_rates["tax_rate"]
        
        # Calculate WACC
        equity_component = equity_share * roe
        debt_component = debt_share * cost_of_debt * (1 - tax_rate)
        wacc = equity_component + debt_component
        
        # Reasonable bounds for WACC (2% to 25%)
        wacc = max(0.02, min(wacc, 0.25))
        
        logger.info(f"📊 Equity Component: {equity_share:.1%} × {roe:.2%} = {equity_component:.2%}")
        logger.info(f"🏦 Debt Component: {debt_share:.1%} × {cost_of_debt:.2%} × (1-{tax_rate:.1%}) = {debt_component:.2%}")
        logger.info(f"⚖️ Final WACC: {wacc:.2%}")
        
        return wacc

    def _log_calculation_summary(self, financial_metrics: Dict[str, Any]) -> None:
        """Log comprehensive calculation summary"""
        logger.info("📊 FINANCIAL METRICS CALCULATION SUMMARY:")
        
        # Company info
        logger.info(f"  🏭 Company: {financial_metrics.get('plant_name', 'Unknown')}")
        logger.info(f"  🌍 Country: {financial_metrics.get('extracted_country', 'Unknown')}")
        logger.info(f"  💱 Currency: {financial_metrics.get('extracted_currency', 'Unknown')}")
        
        # Enhanced metadata
        metadata = financial_metrics.get('calculation_metadata', {})
        logger.info(f"  🌍 Countries Supported: {metadata.get('countries_supported', 0)}")
        logger.info(f"  💱 Currencies Supported: {metadata.get('currencies_supported', 0)}")
        
        # Currency information
        currency_info = financial_metrics.get('currency_information', {})
        if currency_info.get('local_currency') != 'Unknown':
            logger.info(f"  💵 USD Rate: {currency_info.get('usd_exchange_rate', 0):.4f}")
            logger.info(f"  💶 EUR Rate: {currency_info.get('eur_exchange_rate', 0):.4f}")
        
        # Key financial values
        logger.info(f"  🏗️ Gross Block: {financial_metrics.get('gross_block', 0):,.0f}")
        logger.info(f"  📊 WDV IT: {financial_metrics.get('wdv_it', 0):,.0f}")
        logger.info(f"  🏛️ Tax Rate: {financial_metrics.get('corporate_tax_rate_static', 0):.1%}")
        logger.info(f"  🏗️ Depreciation Rate: {financial_metrics.get('depreciation_rate', 0):.1%}")
        logger.info(f"  💰 Transition Credit: {financial_metrics.get('transition_credit_base', 0):,.1f}")
        
        # WACC components
        wacc = financial_metrics.get('wacc_breakup', {})
        logger.info(f"  💰 Cost of Debt: {wacc.get('cost_of_debt', 0):.2%}")
        logger.info(f"  📈 ROE: {wacc.get('roe', 0):.2%}")
        logger.info(f"  ⚖️ WACC: {wacc.get('wacc', 0):.2%}")
        logger.info(f"  🏦 Debt Share: {wacc.get('debt', 0):.1%}")
        logger.info(f"  📈 Equity Share: {wacc.get('equity', 0):.1%}")
        logger.info(f"  💼 Working Capital Interest: {financial_metrics.get('working_capital_interest', 0):.2%}")

    def calculate_and_finalize_wacc(self, financial_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Final WACC calculation and update"""
        logger.info("🔧 Finalizing WACC calculation...")
        
        try:
            wacc_breakup = financial_metrics.get("wacc_breakup", {})
            
            # Extract components
            equity_share = wacc_breakup.get("equity", 0.5)
            debt_share = wacc_breakup.get("debt", 0.5)
            roe = wacc_breakup.get("roe", 0.12)
            cost_of_debt = wacc_breakup.get("cost_of_debt", 0.08)
            tax_rate = wacc_breakup.get("tax_rate", 0.25)
            
            # Calculate final WACC
            final_wacc = self._calculate_final_wacc(
                {"roe": roe, "cost_of_debt": cost_of_debt},
                {"equity_share": equity_share, "debt_share": debt_share},
                {"tax_rate": tax_rate}
            )
            
            # Update WACC in the breakup
            financial_metrics["wacc_breakup"]["wacc"] = final_wacc
            
            logger.info("✅ WACC calculation finalized")
            return financial_metrics
            
        except Exception as e:
            logger.error(f"❌ WACC finalization failed: {e}")
            # Set default WACC if calculation fails
            financial_metrics["wacc_breakup"]["wacc"] = 0.10  # 10% default
            return financial_metrics


# Test function
def test_calculator():
    """Test the enhanced financial metrics calculator"""
    print("🧪 Testing Enhanced Financial Metrics Calculator...")
    
    calculator = FinancialMetricsCalculator()
    
    # Test exchange rate functionality
    print("\n💱 Testing Exchange Rate Functionality:")
    try:
        usd_to_eur = calculator.get_exchange_rate("USD", "EUR")
        print(f"USD to EUR: {usd_to_eur}")
        
        conversion = calculator.convert_currency(1000, "USD", "EUR")
        print(f"Convert 1000 USD: {conversion}")
        
    except Exception as e:
        print(f"Exchange rate test error: {e}")
    
    # Test with sample data
    sample_bs = {
        "balance_sheet": {
            "country": "United Kingdom",  # Test with new country
            "currency": "GBP",           # Test with new currency
            "plant_name": "Test UK Company Ltd",
            "Property, plant and equipment": 50000000000,
            "current_assets": 8000000000,
            "current_liabilities": 12000000000,
            "short_term_borrowings": 4500000000,
            "Long_Term_borrowing": 29000000000,
            "total_equity": 25000000000
        }
    }
    
    sample_pl = {
        "profit_loss": {
            "revenue": 15000000000,
            "finance_cost": 3500000000,
            "net_income": 2800000000
        }
    }
    
    try:
        result = calculator.calculate_financial_metrics(sample_bs, sample_pl)
        print("\n✅ Enhanced calculator test completed successfully!")
        print(f"🎯 WACC calculated: {result['wacc_breakup']['wacc']:.2%}")
        print(f"🌍 Country: {result['extracted_country']}")
        print(f"💱 Currency: {result['extracted_currency']}")
        print(f"🏛️ Tax Rate: {result['corporate_tax_rate_static']:.1%}")
        print(f"🏗️ Depreciation Rate: {result['depreciation_rate']:.1%}")
        
        # Test currency information
        if 'currency_information' in result:
            curr_info = result['currency_information']
            print(f"💵 USD Rate: {curr_info.get('usd_exchange_rate', 0):.4f}")
            print(f"💶 EUR Rate: {curr_info.get('eur_exchange_rate', 0):.4f}")
        
    except Exception as e:
        print(f"❌ Enhanced calculator test failed: {e}")


if __name__ == "__main__":
    test_calculator()