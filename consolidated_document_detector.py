"""
Consolidated Document Detector for Financial Pipeline
Date: 2025-07-08 10:55:47 UTC
User: Gangatharan G
Purpose: Detect consolidated/combined financial documents before processing
Integration: FastAPI pipeline with early stop mechanism
"""

import os
import json
import base64
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import tempfile

# Required imports
try:
    from pdf2image import convert_from_path
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_core.messages import HumanMessage
    from PIL import Image
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Install with: pip install pdf2image langchain-google-genai pillow")
    exit(1)

logger = logging.getLogger(__name__)

class ConsolidatedDocumentDetector:
    """
    Lightweight detector for consolidated/combined financial documents
    Integrates with existing pipeline to stop processing early
    User: Gangatharan G
    Date: 2025-07-08 10:55:47 UTC
    """
    
    DETECTION_PROMPT = """
    You are analyzing the first few pages of a financial document to determine if it's a CONSOLIDATED or COMBINED document.

    🎯 DETECTION CRITERIA:
    
    **CONSOLIDATED DOCUMENT INDICATORS:**
    - Title contains: "Consolidated Financial Statements", "Consolidated Annual Report", "Consolidated Balance Sheet"
    - Multiple subsidiary/entity names mentioned on same pages
    - Phrases like: "Consolidated results", "Group consolidated", "Combined entities"
    - Table headers mentioning "Consolidated" or "Combined"
    - Multiple company codes/registration numbers listed
    - Parent company + subsidiary structure evident
    - Group structure diagrams or subsidiary lists
    - Consolidated profit and loss statements
    - Combined cash flow statements
    
    **STANDALONE DOCUMENT INDICATORS:**
    - Single company name throughout
    - Individual entity financial statements
    - No mention of subsidiaries or group structure
    - Standard individual company annual report format
    - Single entity registration number
    - Individual company auditor's report
    
    **DETECTION RULES:**
    1. If you see "Consolidated" or "Combined" in titles → CONSOLIDATED
    2. If multiple company names/entities on same page → CONSOLIDATED  
    3. If clear parent-subsidiary structure → CONSOLIDATED
    4. If group structure mentioned → CONSOLIDATED
    5. If single company throughout → STANDALONE
    6. If unsure → STANDALONE (don't block processing)
    
    **CONFIDENCE LEVELS:**
    - 0.9+ = Very confident (clear consolidated titles)
    - 0.7-0.9 = Confident (multiple indicators)
    - 0.5-0.7 = Moderate (some indicators)
    - Below 0.5 = Low confidence (classify as STANDALONE)
    
    **IMPORTANT:**
    - Only flag as CONSOLIDATED if you're confident (>70% sure)
    - When in doubt, classify as STANDALONE to allow processing
    - Focus on titles, headers, and company name patterns
    - Look for subsidiary lists or group structures
    **NOTE:**
    - Important Rule: If both Standalone and Consolidated financial indicators are present in the document, treat it as a Standalone document and continue processing.
      Only stop processing if only Consolidated indicators are found and no Standalone indicators are present.
      Do not stop processing just because Consolidated terms appear.
      This rule overrides any other assumptions about document type.
    RETURN ONLY this JSON format:
    {
        "document_type": "CONSOLIDATED" or "STANDALONE",
        "confidence": 0.85,
        "indicators": ["Consolidated Balance Sheet title", "Multiple entity names"],
        "recommendation": "STOP_PROCESSING" or "CONTINUE_PROCESSING",
        "reasoning": "Brief explanation of decision"
    }
    """
    
    def __init__(self):
        """Initialize the detector with Gemini AI"""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY required for consolidated detection")
        
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-001",
            temperature=0,
            max_tokens=None,
            timeout=None,
            google_api_key=api_key,
            max_retries=2,
        )
        
        logger.info("🔍 Consolidated Document Detector initialized")
        logger.info("👤 User: Gangatharan G")
        logger.info("📅 Date: 2025-07-08 10:55:47 UTC")
    
    def encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64 for API"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Error encoding image {image_path}: {e}")
            raise
    
    def extract_first_pages(self, pdf_path: str, max_pages: int = 10) -> List[str]:
        """Extract first few pages from PDF for analysis"""
        image_paths = []
        try:
            pdf_path = Path(pdf_path)
            if not pdf_path.exists():
                raise FileNotFoundError(f"PDF not found: {pdf_path}")
            
            logger.info(f"🔍 Extracting first {max_pages} pages from: {pdf_path.name}")
            
            # Convert first few pages to images
            images = convert_from_path(
                pdf_path, 
                dpi=200,  # Higher DPI for better text recognition
                first_page=1, 
                last_page=max_pages,
                fmt='PNG'
            )
            
            # Create temp directory for this detection
            temp_dir = Path(tempfile.mkdtemp(prefix="consolidated_detection_"))
            
            for i, image in enumerate(images, 1):
                image_path = temp_dir / f"detection_page_{i}.png"
                image.save(image_path, 'PNG')
                image_paths.append(str(image_path))
            
            logger.info(f"✅ Extracted {len(image_paths)} pages for analysis")
            return image_paths
            
        except Exception as e:
            logger.error(f"Error extracting pages from {pdf_path}: {e}")
            # Cleanup on error
            for img_path in image_paths:
                try:
                    os.remove(img_path)
                except:
                    pass
            raise
    
    def detect_consolidated_document(self, pdf_path: str) -> Dict:
        """
        Detect if document is consolidated/combined
        Returns: {
            "is_consolidated": bool,
            "confidence": float,
            "indicators": List[str],
            "recommendation": str,
            "document_type": str,
            "reasoning": str
        }
        """
        image_paths = []
        temp_dir = None
        
        try:
            logger.info(f"🔍 CONSOLIDATED DETECTION STARTED: {Path(pdf_path).name}")
            logger.info(f"👤 User: Gangatharan G")
            logger.info(f"📅 Time: 2025-07-08 10:55:47 UTC")
            
            # Extract first 3 pages
            image_paths = self.extract_first_pages(pdf_path, max_pages=10)
            
            if not image_paths:
                raise Exception("No pages extracted from PDF")
            
            # Store temp directory for cleanup
            temp_dir = Path(image_paths[0]).parent
            
            # Prepare AI content
            content = [{"type": "text", "text": self.DETECTION_PROMPT}]
            
            # Add images with clear labels
            for i, image_path in enumerate(image_paths, 1):
                try:
                    image_base64 = self.encode_image_base64(image_path)
                    content.append({
                        "type": "text", 
                        "text": f"\n=== PAGE {i} OF DOCUMENT ===\nAnalyze this page for consolidated/combined indicators:"
                    })
                    content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}"
                        }
                    })
                except Exception as e:
                    logger.warning(f"Failed to process page {i}: {e}")
                    continue
            
            # Get AI response
            logger.info("🤖 Sending pages to Gemini AI for analysis...")
            message = HumanMessage(content=content)
            response = self.llm.invoke([message])
            
            # Parse response
            response_text = response.content.strip()
            
            # Clean JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            # Parse JSON
            try:
                detection_result = json.loads(response_text)
                
                is_consolidated = detection_result.get("document_type") == "CONSOLIDATED"
                confidence = detection_result.get("confidence", 0.0)
                indicators = detection_result.get("indicators", [])
                recommendation = detection_result.get("recommendation", "CONTINUE_PROCESSING")
                reasoning = detection_result.get("reasoning", "No reasoning provided")
                
                result = {
                    "is_consolidated": is_consolidated,
                    "confidence": confidence,
                    "indicators": indicators,
                    "recommendation": recommendation,
                    "document_type": detection_result.get("document_type", "STANDALONE"),
                    "reasoning": reasoning,
                    "pdf_name": Path(pdf_path).name,
                    "detection_timestamp": "2025-07-08 10:55:47",
                    "user": "Gangatharan G"
                }
                
                # Log results
                if is_consolidated and confidence > 0.7:
                    logger.warning("🚨 CONSOLIDATED DOCUMENT DETECTED!")
                    logger.warning(f"   📄 File: {Path(pdf_path).name}")
                    logger.warning(f"   🎯 Confidence: {confidence:.1%}")
                    logger.warning(f"   📋 Indicators: {', '.join(indicators)}")
                    logger.warning(f"   💭 Reasoning: {reasoning}")
                    logger.warning(f"   ⚠️  Recommendation: {recommendation}")
                elif is_consolidated:
                    logger.info(f"🔍 Possible consolidated document (low confidence: {confidence:.1%})")
                    logger.info(f"   📄 File: {Path(pdf_path).name}")
                    logger.info(f"   ✅ Allowing processing to continue")
                else:
                    logger.info(f"✅ STANDALONE DOCUMENT CONFIRMED")
                    logger.info(f"   📄 File: {Path(pdf_path).name}")
                    logger.info(f"   🎯 Confidence: {confidence:.1%}")
                
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ Failed to parse AI response: {e}")
                logger.error(f"🔍 Raw response: {response_text}")
                
                # Return safe default to not block processing
                return {
                    "is_consolidated": False,
                    "confidence": 0.0,
                    "indicators": ["AI parsing failed"],
                    "recommendation": "CONTINUE_PROCESSING",
                    "document_type": "STANDALONE",
                    "reasoning": f"Detection failed due to JSON parsing error: {str(e)}",
                    "pdf_name": Path(pdf_path).name,
                    "detection_timestamp": "2025-07-08 10:55:47",
                    "user": "Gangatharan G"
                }
                
        except Exception as e:
            logger.error(f"❌ DETECTION ERROR: {e}")
            logger.error(f"📄 File: {Path(pdf_path).name}")
            
            # Return safe default to not block processing on errors
            return {
                "is_consolidated": False,
                "confidence": 0.0,
                "indicators": [f"Detection failed: {str(e)}"],
                "recommendation": "CONTINUE_PROCESSING",
                "document_type": "STANDALONE",
                "reasoning": f"Detection failed due to error: {str(e)}",
                "pdf_name": Path(pdf_path).name,
                "detection_timestamp": "2025-07-08 10:55:47",
                "user": "Gangatharan G"
            }
        
        finally:
            # Cleanup temp images and directory
            for image_path in image_paths:
                try:
                    os.remove(image_path)
                except:
                    pass
            
            if temp_dir and temp_dir.exists():
                try:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                except:
                    pass
            
            logger.info("🧹 Cleaned up temporary detection files")

# Integration functions for your API
def check_consolidated_documents(uploaded_files: List[str], confidence_threshold: float = 0.7) -> Tuple[bool, Dict]:
    """
    Check multiple uploaded files for consolidated documents
    
    Args:
        uploaded_files: List of PDF file paths
        confidence_threshold: Minimum confidence to stop processing (default: 0.7)
    
    Returns:
        Tuple of (should_stop, detection_results)
    """
    logger.info("🔍 BATCH CONSOLIDATED DETECTION STARTED")
    logger.info(f"👤 User: Gangatharan G")
    logger.info(f"📅 Time: 2025-07-08 10:55:47 UTC")
    logger.info(f"📊 Files to check: {len(uploaded_files)}")
    logger.info(f"🎯 Confidence threshold: {confidence_threshold:.1%}")
    
    detector = ConsolidatedDocumentDetector()
    all_results = {}
    consolidated_found = False
    consolidated_files = []
    
    for file_path in uploaded_files:
        try:
            file_name = Path(file_path).name
            logger.info(f"🔍 Checking file: {file_name}")
            
            result = detector.detect_consolidated_document(file_path)
            all_results[file_name] = result
            
            # Check if we should stop processing
            if (result["is_consolidated"] and 
                result["confidence"] > confidence_threshold and 
                result["recommendation"] == "STOP_PROCESSING"):
                
                consolidated_found = True
                consolidated_files.append(file_name)
                logger.warning(f"🚨 STOPPING: Consolidated document detected in {file_name}")
                logger.warning(f"   🎯 Confidence: {result['confidence']:.1%}")
                logger.warning(f"   📋 Indicators: {', '.join(result['indicators'])}")
        
        except Exception as e:
            logger.error(f"❌ Detection failed for {file_path}: {e}")
            # Don't block processing on detection errors
            all_results[Path(file_path).name] = {
                "is_consolidated": False,
                "confidence": 0.0,
                "indicators": [f"Detection error: {str(e)}"],
                "recommendation": "CONTINUE_PROCESSING",
                "document_type": "STANDALONE",
                "reasoning": f"Detection failed due to error: {str(e)}",
                "pdf_name": Path(file_path).name,
                "detection_timestamp": "2025-07-08 10:55:47",
                "user": "Gangatharan G"
            }
    
    # Summary logging
    if consolidated_found:
        logger.warning("🚨 CONSOLIDATED DETECTION SUMMARY:")
        logger.warning(f"   📊 Total files checked: {len(uploaded_files)}")
        logger.warning(f"   ⚠️  Consolidated files found: {len(consolidated_files)}")
        logger.warning(f"   📋 Consolidated files: {', '.join(consolidated_files)}")
        logger.warning(f"   🛑 Processing will be STOPPED")
    else:
        logger.info("✅ CONSOLIDATED DETECTION SUMMARY:")
        logger.info(f"   📊 Total files checked: {len(uploaded_files)}")
        logger.info(f"   ✅ All files are STANDALONE")
        logger.info(f"   🚀 Processing will CONTINUE")
    
    return consolidated_found, all_results

def check_single_consolidated_document(pdf_path: str, confidence_threshold: float = 0.7) -> Tuple[bool, Dict]:
    """
    Check a single PDF for consolidated document
    
    Args:
        pdf_path: Path to PDF file
        confidence_threshold: Minimum confidence to stop processing
    
    Returns:
        Tuple of (should_stop, detection_result)
    """
    detector = ConsolidatedDocumentDetector()
    result = detector.detect_consolidated_document(pdf_path)
    
    should_stop = (result["is_consolidated"] and 
                   result["confidence"] > confidence_threshold and 
                   result["recommendation"] == "STOP_PROCESSING")
    
    return should_stop, result