"""
Multi-Plant Financial Processing API with S3 Integration
Date: 2025-07-01 04:21:12 UTC
User: Gangatharan G
Purpose: FastAPI application for worldwide plant financial analysis
"""

import os
import json
import uuid
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from io import BytesIO

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, validator
import uvicorn

# Import your existing components
from multi_year_simple_integration import MultiYearFinancialProcessor
from final_schema_transformer import FinalSchemaTransformer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# FastAPI app initialization
app = FastAPI(
    title="Multi-Plant Financial Processing API",
    description="Worldwide plant financial analysis with S3 storage",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Global job storage (in production, use Redis or database)
jobs_storage = {}

# AWS S3 Configuration
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
S3_BUCKET = "aws-demo-22"

# Pydantic models
class PlantProcessRequest(BaseModel):
    plant_name: str
    years: Optional[List[int]] = None
    use_web_search: bool = True
    
    @validator('plant_name')
    def validate_plant_name(cls, v):
        if not v or len(v.strip()) < 3:
            raise ValueError('Plant name must be at least 3 characters long')
        return v.strip()
    
    @validator('years')
    def validate_years(cls, v):
        if v:
            for year in v:
                if year < 2000 or year > 2030:
                    raise ValueError('Years must be between 2000 and 2030')
        return v

class PlantProcessLocalRequest(BaseModel):
    plant_name: str
    
    @validator('plant_name')
    def validate_plant_name(cls, v):
        if not v or len(v.strip()) < 3:
            raise ValueError('Plant name must be at least 3 characters long')
        return v.strip()

class JobResponse(BaseModel):
    job_id: str
    status: str
    plant_name: str
    created_at: str
    message: str

class ProcessingResult(BaseModel):
    job_id: str
    status: str
    plant_name: str
    s3_path: Optional[str] = None
    processing_time: Optional[str] = None
    years_processed: Optional[List[int]] = None
    currency: Optional[str] = None
    download_url: Optional[str] = None
    error_message: Optional[str] = None
    created_at: str
    completed_at: Optional[str] = None

# AWS S3 Client
class S3Manager:
    def __init__(self):
        self.s3_client = None
        self.initialize_s3_client()
    
    def initialize_s3_client(self):
        """Initialize S3 client with credentials"""
        try:
            if not AWS_ACCESS_KEY_ID or not AWS_SECRET_ACCESS_KEY:
                raise NoCredentialsError()
            
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=AWS_ACCESS_KEY_ID,
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
                region_name=AWS_REGION
            )
            logger.info("✅ S3 client initialized successfully")
        except NoCredentialsError:
            logger.error("❌ AWS credentials not found")
            raise HTTPException(
                status_code=500, 
                detail="AWS credentials not configured"
            )
        except Exception as e:
            logger.error(f"❌ Failed to initialize S3 client: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail=f"S3 initialization failed: {str(e)}"
            )
    
    def create_plant_folder_name(self, plant_name: str) -> str:
        """Create S3-safe folder name from plant name"""
        return plant_name.lower().replace(" ", "_").replace('"', '').replace("'", "")
    
    def upload_json_to_s3(self, json_data: Dict, plant_name: str, filename: str) -> str:
        """Upload JSON data to S3"""
        try:
            folder_name = self.create_plant_folder_name(plant_name)
            s3_key = f"{folder_name}/{filename}"
            
            # Convert to JSON string
            json_string = json.dumps(json_data, indent=2, ensure_ascii=False, default=str)
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=S3_BUCKET,
                Key=s3_key,
                Body=json_string.encode('utf-8'),
                ContentType='application/json'
            )
            
            s3_path = f"{S3_BUCKET}/{s3_key}"
            logger.info(f"✅ Uploaded to S3: {s3_path}")
            return s3_path
            
        except ClientError as e:
            logger.error(f"❌ S3 upload failed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload to S3: {str(e)}"
            )
    
    def generate_download_url(self, s3_path: str, expiration: int = 3600) -> str:
        """Generate presigned download URL"""
        try:
            # Extract key from full path
            s3_key = s3_path.replace(f"{S3_BUCKET}/", "")
            
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': S3_BUCKET, 'Key': s3_key},
                ExpiresIn=expiration
            )
            return url
        except Exception as e:
            logger.error(f"❌ Failed to generate download URL: {str(e)}")
            return ""
    
    def list_plant_files(self, plant_name: str) -> List[Dict]:
        """List all files for a plant"""
        try:
            folder_name = self.create_plant_folder_name(plant_name)
            
            response = self.s3_client.list_objects_v2(
                Bucket=S3_BUCKET,
                Prefix=f"{folder_name}/"
            )
            
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat(),
                        'download_url': self.generate_download_url(f"{S3_BUCKET}/{obj['Key']}")
                    })
            
            return files
        except Exception as e:
            logger.error(f"❌ Failed to list plant files: {str(e)}")
            return []

# Initialize S3 Manager
s3_manager = S3Manager()

# Background processing function
async def process_plant_background(
    job_id: str,
    plant_name: str,
    years: Optional[List[int]] = None, 
    local_pdf_paths: Optional[List[str]] = None
):
    """Background task for processing plant data"""
    start_time = datetime.utcnow()
    
    # Update job status
    jobs_storage[job_id].update({
        "status": "processing",
        "started_at": start_time.isoformat()
    })
    
    try:
        logger.info(f"🚀 Starting background processing for {plant_name} (Job: {job_id})")
        
        # Initialize processor
        processor = MultiYearFinancialProcessor()
        
        # Process plant data
        output_path, results = await processor.process_plant_multi_year(
            plant_name=plant_name,
            use_local_pdfs=local_pdf_paths,
            target_years=years
        )
        
        if not output_path:
            raise Exception("Processing failed - no output generated")
        
        # Transform to final schema
        logger.info(f"🔄 Transforming to final schema for {plant_name}")
        schema_transformer = FinalSchemaTransformer()
        
        final_schema_output = schema_transformer.transform_to_final_schema({
            "merged_results": results.get("merged_results", {}),
            "merged_debt_equity_analysis": results.get("merged_results", {}).get("merged_debt_equity_analysis", []),
            "merged_financial_data": results.get("merged_results", {}).get("merged_financial_data", {}),
            "detailed_year_results": results.get("year_results", []),
            "plant_name": plant_name
        })
        
        # Upload to S3
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        filename = f"final_schema_{timestamp}.json"
        
        logger.info(f"☁️ Uploading to S3 for {plant_name}")
        s3_path = s3_manager.upload_json_to_s3(final_schema_output, plant_name, filename)
        
        # Generate download URL
        download_url = s3_manager.generate_download_url(s3_path)
        
        # Calculate processing time
        end_time = datetime.utcnow()
        processing_time = str(end_time - start_time)
        
        # Extract successful years and currency
        successful_years = results.get("merged_results", {}).get("successful_years", [])
        detected_currency = final_schema_output.get("currency", "Unknown")
        
        # Update job with success
        jobs_storage[job_id].update({
            "status": "completed",
            "s3_path": s3_path,
            "processing_time": processing_time,
            "years_processed": successful_years,
            "currency": detected_currency,
            "download_url": download_url,
            "completed_at": end_time.isoformat(),
            "final_output": final_schema_output
        })
        
        logger.info(f"✅ Successfully completed processing for {plant_name} (Job: {job_id})")
        
    except Exception as e:
        error_msg = f"Processing failed for {plant_name}: {str(e)}"
        logger.error(f"❌ {error_msg}")
        
        # Update job with failure
        jobs_storage[job_id].update({
            "status": "failed",
            "error_message": error_msg,
            "completed_at": datetime.utcnow().isoformat()
        })

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Multi-Plant Financial Processing API",
        "version": "1.0.0",
        "current_time": datetime.utcnow().isoformat(),
        "user": "Gangatharan G",
        "features": [
            "Worldwide plant processing",
            "Multi-year analysis", 
            "Local PDF upload",
            "Web search capability",
            "AWS S3 storage",
            "Final schema output"
        ],
        "endpoints": {
            "process_plant": "POST /process-plant/",
            "process_local": "POST /process-plant-local/",
            "job_status": "GET /status/{job_id}",
            "plant_results": "GET /plants/{plant_name}/results"
        }
    }

@app.post("/process-plant/", response_model=JobResponse)
async def process_plant(
    request: PlantProcessRequest,
    background_tasks: BackgroundTasks
):
    """Process plant with web search for PDFs"""
    
    # Generate unique job ID
    job_id = str(uuid.uuid4())
    current_time = datetime.utcnow().isoformat()
    
    # Store job info
    jobs_storage[job_id] = {
        "job_id": job_id,
        "status": "queued",
        "plant_name": request.plant_name,
        "years": request.years,
        "use_web_search": request.use_web_search,
        "created_at": current_time,
        "user": "Gangatharan G"
    }
    
    # Add background task
    background_tasks.add_task(
        process_plant_background,
        job_id=job_id,
        plant_name=request.plant_name,
        years=request.years,
        local_pdf_paths=None
    )
    
    logger.info(f"🎯 Queued processing job for {request.plant_name} (Job: {job_id})")
    
    return JobResponse(
        job_id=job_id,
        status="queued",
        plant_name=request.plant_name,
        created_at=current_time,
        message=f"Processing queued for {request.plant_name}. Use /status/{job_id} to check progress."
    )

@app.post("/process-plant-local/", response_model=JobResponse)
async def process_plant_local(
    plant_name: str,
    files: List[UploadFile] = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Process plant with uploaded local PDFs"""
    
    # Validate files
    if not files:
        raise HTTPException(status_code=400, detail="No files uploaded")
    
    # Validate file types
    for file in files:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid file type: {file.filename}. Only PDF files are allowed."
            )
    
    # Generate unique job ID
    job_id = str(uuid.uuid4())
    current_time = datetime.utcnow().isoformat()
    
    try:
        # Save uploaded files
        upload_dir = Path(f"uploads/{job_id}")
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        local_pdf_paths = []
        for file in files:
            file_path = upload_dir / file.filename
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            local_pdf_paths.append(str(file_path))
        
        # Store job info
        jobs_storage[job_id] = {
            "job_id": job_id,
            "status": "queued",
            "plant_name": plant_name,
            "uploaded_files": [f.filename for f in files],
            "local_pdf_paths": local_pdf_paths,
            "created_at": current_time,
            "user": "Gangatharan G"
        }
        
        # Add background task
        background_tasks.add_task(
            process_plant_background,
            job_id=job_id,
            plant_name=plant_name,
            years=None,
            local_pdf_paths=local_pdf_paths
        )
        
        logger.info(f"🎯 Queued local processing job for {plant_name} with {len(files)} files (Job: {job_id})")
        
        return JobResponse(
            job_id=job_id,
            status="queued",
            plant_name=plant_name,
            created_at=current_time,
            message=f"Processing queued for {plant_name} with {len(files)} uploaded files. Use /status/{job_id} to check progress."
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to process uploaded files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process uploaded files: {str(e)}")

@app.get("/status/{job_id}", response_model=ProcessingResult)
async def get_job_status(job_id: str):
    """Get processing job status"""
    
    if job_id not in jobs_storage:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_data = jobs_storage[job_id]
    
    return ProcessingResult(
        job_id=job_data["job_id"],
        status=job_data["status"],
        plant_name=job_data["plant_name"],
        s3_path=job_data.get("s3_path"),
        processing_time=job_data.get("processing_time"),
        years_processed=job_data.get("years_processed"),
        currency=job_data.get("currency"),
        download_url=job_data.get("download_url"),
        error_message=job_data.get("error_message"),
        created_at=job_data["created_at"],
        completed_at=job_data.get("completed_at")
    )

@app.get("/plants/{plant_name}/results")
async def get_plant_results(plant_name: str):
    """Get all results for a specific plant from S3"""
    
    try:
        files = s3_manager.list_plant_files(plant_name)
        
        return {
            "plant_name": plant_name,
            "total_files": len(files),
            "files": files,
            "s3_folder": f"{S3_BUCKET}/{s3_manager.create_plant_folder_name(plant_name)}/"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get plant results: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve plant results: {str(e)}")

@app.get("/jobs")
async def list_all_jobs():
    """List all processing jobs"""
    
    jobs_list = []
    for job_id, job_data in jobs_storage.items():
        jobs_list.append({
            "job_id": job_id,
            "plant_name": job_data["plant_name"],
            "status": job_data["status"],
            "created_at": job_data["created_at"],
            "completed_at": job_data.get("completed_at"),
            "currency": job_data.get("currency"),
            "years_processed": job_data.get("years_processed")
        })
    
    return {
        "total_jobs": len(jobs_list),
        "jobs": sorted(jobs_list, key=lambda x: x["created_at"], reverse=True)
    }

@app.delete("/jobs/{job_id}")
async def delete_job(job_id: str):
    """Delete a specific job"""
    
    if job_id not in jobs_storage:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_data = jobs_storage.pop(job_id)
    
    # Clean up uploaded files if they exist
    if "local_pdf_paths" in job_data:
        upload_dir = Path(f"uploads/{job_id}")
        if upload_dir.exists():
            import shutil
            shutil.rmtree(upload_dir)
    
    return {"message": f"Job {job_id} deleted successfully"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    
    try:
        # Test S3 connection
        s3_manager.s3_client.head_bucket(Bucket=S3_BUCKET)
        s3_status = "healthy"
    except Exception as e:
        s3_status = f"unhealthy: {str(e)}"
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "s3_bucket": S3_BUCKET,
        "s3_status": s3_status,
        "active_jobs": len([j for j in jobs_storage.values() if j["status"] == "processing"]),
        "total_jobs": len(jobs_storage)
    }

# Exception handlers
@app.exception_handler(ValueError)
async def value_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"detail": str(exc)}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"❌ Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize API on startup"""
    logger.info("🚀 Multi-Plant Financial Processing API Starting...")
    logger.info(f"📅 Date: 2025-07-01 04:21:12 UTC")
    logger.info(f"👤 User: Gangatharan G")
    logger.info(f"☁️ S3 Bucket: {S3_BUCKET}")
    logger.info("✅ API Ready!")

if __name__ == "__main__":
    # Run the API server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )