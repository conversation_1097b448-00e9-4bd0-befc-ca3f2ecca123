# web_search.py
import os
import requests
import urllib3
from pathlib import Path
from bs4 import BeautifulSoup
from dotenv import load_dotenv

# Load API key
load_dotenv()
SCRAPER_API_KEY = os.getenv("SCRAPER_API_KEY")
SCRAPER_API_URL = "https://api.scraperapi.com/structured/google/search"
HEADERS = {"User-Agent": "Mozilla/5.0"}

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# Function to search for the PDF URL using ScraperAPI (for direct download)
def search_pdf_url(query: str):
    params = {
        "api_key": SCRAPER_API_KEY,
        "query": query,
        "num": 5
    }

    try:
        response = requests.get(SCRAPER_API_URL, params=params, headers=HEADERS)
        response.raise_for_status()
        results = response.json().get("organic_results", [])

        # Get the first valid PDF URL
        for result in results:
            link = result.get("link", "")
            if link.lower().endswith(".pdf"):
                return link

    except Exception as e:
        print(f"❌ Error during PDF search: {e}")

    return None


# Function to download a PDF from a URL
def download_pdf(url: str, dest_path: Path):
    try:
        r = requests.get(url, stream=True, timeout=15, verify=False)
        r.raise_for_status()
        with open(dest_path, "wb") as f:
            for chunk in r.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
        print(f"✅ Downloaded: {dest_path}")
    except Exception as e:
        print(f"❌ Failed to download {url}: {e}")


# Function to search and scrape PDF links from an HTML page using ScraperAPI
def download_pdfs_from_url(query: str, plant_name: str, limit_no_reports: int = None):
    params = {
        "api_key": SCRAPER_API_KEY,
        "query": query,
        "num": 5
    }

    try:
        response = requests.get(SCRAPER_API_URL, params=params, headers=HEADERS)
        response.raise_for_status()
        results = response.json().get("organic_results", [])

        # Get the first valid URL to scrape
        for result in results:
            url = result.get("link", "")
            if url.startswith("http"):
                base_url = url.split("/")[0] + "//" + url.split("/")[2]
                downloads_dir = os.path.join(os.getcwd(), "downloads", plant_name)
                os.makedirs(downloads_dir, exist_ok=True)

                try:
                    page_response = requests.get(url, timeout=10)
                    page_response.raise_for_status()
                except Exception as e:
                    print(f"❌ Failed to retrieve page: {url} – {e}")
                    continue

                soup = BeautifulSoup(page_response.text, "html.parser")
                pdf_links = [a['href'] for a in soup.find_all('a', href=True) if a['href'].lower().endswith('.pdf')]

                count = 0
                for link in pdf_links:
                    if limit_no_reports is not None and count >= limit_no_reports:
                        break

                    if link.startswith("/"):
                        link = base_url + link
                    elif not link.startswith("http"):
                        link = base_url + "/" + link

                    filename = os.path.basename(link)
                    filepath = os.path.join(downloads_dir, filename)

                    print(f"⬇️ Downloading {link} to {filepath}")
                    try:
                        file_response = requests.get(link, timeout=15)
                        with open(filepath, "wb") as f:
                            f.write(file_response.content)
                        print(f"✅ Saved: {filename}")
                        count += 1
                    except Exception as download_err:
                        print(f"❌ Failed to download {link}: {download_err}")
                return
    except Exception as e:
        print(f"❌ Error during URL search for page scraping: {e}")



# Main runner function


def get_pdf_file_paths(plant_name, base_dir="downloads"):
    folder_name = plant_name.replace(" ", "_").replace('"', '')
    plant_dir = Path("downloads") / folder_name

    if not os.path.isdir(plant_dir):
        raise FileNotFoundError(f"Directory not found: {plant_dir}")

    pdf_paths = [
    str(file_path)
    for file_path in plant_dir.glob("*.pdf")]

    
    return pdf_paths

from langchain.document_loaders import UnstructuredPDFLoader

def extract_first_5_pages_text_langchain(pdf_path, max_pages=5, languages=["eng"]):
    try:
        loader = UnstructuredPDFLoader(
            file_path=pdf_path,
            strategy="auto",  # auto-detect scanned or digital
            languages=["eng"]
        )
        documents = loader.load()

        # Combine content of only first N pages
        text = ""
        for i, doc in enumerate(documents):
            if i >= max_pages:
                break
            text += doc.page_content + "\n"

        return text.strip()

    except Exception as e:
        print(f"Error extracting {pdf_path}: {e}")
        return ""
    


from openai import OpenAI
from dotenv import load_dotenv
import os

# Load API key from .env
load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def is_valid_annual_report(text, plant_name, model="gpt-4o"):
    system_prompt = (
        "You are a document classification assistant. Your task is to check whether a given document "
        "is an annual report and whether it belongs to the specified power plant. Additionally, determine "
        "whether both standalone and consolidated reports are included."
    )

    user_prompt = f"""
    You are given the beginning of a document. Please answer YES or NO only for each question.

    1. Is this document an *annual report*?
    2. Does this annual report belong to the power plant named "{plant_name}"?
    3. Does the document include both *standalone* and *consolidated* financial reports?

    Respond strictly in this format:
    Annual report: YES or NO along with the reason  
    Belongs to plant: YES or NO along with the reason  
    Both standalone and consolidated: YES or NO along with the reason

    Here is the text:
    {text[:100000]}

    NOTE: The power plant name may be abbreviated or phrased differently. Use judgment in matching it.
    """

    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0
        )

        content = response.choices[0].message.content
        print("🔍 GPT-4o Reasoning:\n" + content.strip() + "\n")

        # Convert to lowercase for consistent checks
        content_lower = content.lower()

        # Extract flags
        is_annual = "annual report: yes" in content_lower
        is_correct_plant = "belongs to plant: yes" in content_lower
        has_both_reports = "both standalone and consolidated: yes" in content_lower

        return is_annual and is_correct_plant and has_both_reports

    except Exception as e:
        print(f"❌ Error verifying report: {e}")
        return False




import os

def delete_if_invalid(flag: bool, pdf_path: str):
    if flag:
        print(f"✅ Keeping: {pdf_path}")
        return True
    else:
        try:
            os.remove(pdf_path)
            print(f"🗑️ Deleted: {pdf_path}")
            return False
        except Exception as e:
            print(f"❌ Failed to delete {pdf_path}: {e}")
            return False


def process_pdf_list_with_validation(pdf_list, plant_name: str, model: str = "gpt-4o", max_pages: int = 5):
    for each_pdf in pdf_list:
        print(f"Processing: {each_pdf}")
        text = extract_first_5_pages_text_langchain(each_pdf, max_pages=max_pages)
        validation_flag = is_valid_annual_report(text, plant_name, model=model)
        print(f"Validation result for {each_pdf}: {validation_flag}")
        delete_if_invalid(validation_flag, each_pdf)


def run_downloader(plant_name: str, from_year: int, to_year: int):
    folder_name = plant_name.replace(" ", "_").replace('"', '')
    save_dir = Path("downloads") / folder_name
    save_dir.mkdir(parents=True, exist_ok=True)

    for year in range(from_year, to_year + 1):
        print(f"\n🔍 Searching: {plant_name} - {year}")
        
        # Construct query for direct PDF download
        query_pdf = f'"{plant_name}" power plant annual report {year} filetype:pdf'

        # Step 1: Try to find the PDF URL using the query
        pdf_url = search_pdf_url(query_pdf)
        
        if pdf_url:
            # Step 2: If PDF is found, download it
            pdf_path = save_dir / f"{year}.pdf"
            download_pdf(pdf_url, pdf_path)
    print(f"⚠️ No direct PDF found for {year}. Trying page scraping...")
            
    # Step 3: If no PDF is found, scrape the webpage for PDFs
    query_page = f'"{plant_name}" Investor Relations Annual Report'
    download_pdfs_from_url(query_page, folder_name,5)
    pdf_list = get_pdf_file_paths(plant_name)
    process_pdf_list_with_validation(pdf_list,plant_name)